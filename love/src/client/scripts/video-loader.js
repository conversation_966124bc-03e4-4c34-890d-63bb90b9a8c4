/**
 * 智能视频加载器 - 四层CDN降级架构
 * 整合现有页面分散的视频加载逻辑，实现统一的智能加载管理
 * 
 * 四层架构：
 * 1. Primary: Cloudflare R2 (6秒超时)
 * 2. Secondary: Cloudinary多账户 (7秒超时)  
 * 3. Tertiary: VPS本地服务 (10秒超时)
 * 4. Quaternary: 星空背景保障 (2秒超时)
 * 
 * @version 1.0.0
 * <AUTHOR> Project Team
 */

class VideoLoader {
    constructor() {
        this.config = null;
        this.loadingOrder = ['primary', 'secondary', 'tertiary', 'quaternary'];
        this.currentAttempt = 0;
        this.maxRetries = 1;

        // 生成唯一实例ID，用于调试并发问题
        this.instanceId = Math.random().toString(36).substring(2, 11);

        // 页面主题背景映射 (保持现有页面特色)
        this.themeBackgrounds = {
            home: 'linear-gradient(135deg, #fce7f3 0%, #fbcfe8 25%, #f9a8d4 50%, #f472b6 75%, #ec4899 100%)', // 花朵主题
            anniversary: 'linear-gradient(135deg, #fef3c7 0%, #fde68a 25%, #fbbf24 50%, #f59e0b 75%, #d97706 100%)', // 浪漫金色
            meetings: 'linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%)', // 星河主题
            memorial: 'linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 25%, #4fc3f7 50%, #29b6f6 75%, #0288d1 100%)', // 海洋主题
            'together-days': 'linear-gradient(135deg, #fff3e0 0%, #ffe0b2 25%, #ffcc02 50%, #ff9800 75%, #f57c00 100%)' // 夕阳主题
        };

        // 加载状态管理 - 改为请求级别，避免并发冲突
        this.loadingState = {
            initialized: false,
            currentLayer: null,
            startTime: null,
            totalAttempts: 0
        };

        console.log(`🆔 VideoLoader实例创建: ${this.instanceId}`);
    }

    /**
     * 初始化加载器，从API获取配置
     */
    async init() {
        if (this.config) return; // 避免重复初始化
        
        try {
            console.log('🎬 初始化智能视频加载器...');
            const response = await fetch('/api/config');
            const result = await response.json();
            
            if (result.success && result.data.videoDelivery) {
                this.config = result.data.videoDelivery;
                this.loadingOrder = this.config.macroConfig?.loadingOrder || this.loadingOrder;
                
                console.log(`📋 加载策略: ${this.config.macroConfig?.strategy || 'DEFAULT'}`);
                console.log(`🔄 加载顺序: ${this.loadingOrder.join(' → ')}`);
                
                this.loadingState.initialized = true;
            } else {
                throw new Error('视频配置获取失败');
            }
        } catch (error) {
            console.error('❌ 配置加载失败:', error);
            // 使用默认配置
            this.loadingOrder = ['tertiary', 'quaternary']; // 降级到VPS和星空背景
        }
    }

    /**
     * 智能视频加载主函数
     * @param {string} pageName - 页面名称 (home, anniversary, meetings, memorial, together-days)
     * @param {HTMLVideoElement} videoElement - 视频元素
     * @param {Object} options - 加载选项
     * @returns {Promise<boolean>} 加载是否成功
     */
    async loadVideo(pageName, videoElement, options = {}) {
        if (!this.config) await this.init();

        // 创建请求级别的状态，避免并发冲突
        const requestState = {
            startTime: Date.now(),
            totalAttempts: 0,
            requestId: Math.random().toString(36).substring(2, 11)
        };

        console.log(`🎬 [${this.instanceId}:${requestState.requestId}] 开始智能加载视频: ${pageName}`);
        console.log(`📋 [${this.instanceId}] 使用策略: ${this.loadingOrder.join(' → ')}`);

        // 保存原始的错误处理函数
        const originalOnError = options.onError;
        
        for (let i = 0; i < this.loadingOrder.length; i++) {
            const layerKey = this.loadingOrder[i];
            const layerConfig = this.config.layers[layerKey];

            if (!layerConfig || !layerConfig.enabled) {
                console.log(`⏭️ [${requestState.requestId}] 跳过禁用层: ${layerKey}`);
                continue;
            }

            requestState.totalAttempts++;

            try {
                console.log(`🔄 [${requestState.requestId}] 尝试第${i + 1}层: ${layerKey} (${layerConfig.type})`);

                // 特殊处理星空背景层
                if (layerKey === 'quaternary') {
                    console.log(`✨ [${requestState.requestId}] 启用星空背景保障`);
                    return await this.loadStarryBackground(pageName, videoElement, options);
                }

                const url = this.generateVideoUrl(pageName, layerKey);
                if (!url) {
                    console.warn(`⚠️ [${requestState.requestId}] ${layerKey} URL生成失败`);
                    continue;
                }

                console.log(`🔗 [${requestState.requestId}] 尝试加载: ${url}`);

                const success = await this.loadWithTimeout(
                    url,
                    videoElement,
                    layerConfig.timeout,
                    {
                        ...options,
                        layerInfo: { key: layerKey, type: layerConfig.type },
                        requestId: requestState.requestId
                    }
                );

                if (success) {
                    const elapsed = Date.now() - requestState.startTime;
                    console.log(`✅ [${requestState.requestId}] 视频加载成功: ${layerKey} (${elapsed}ms)`);

                    // 调用成功回调
                    if (options.onSuccess) {
                        options.onSuccess(layerKey);
                    }

                    return true;
                }

            } catch (error) {
                console.warn(`❌ [${requestState.requestId}] ${layerKey} 加载失败: ${error.message}`);

                // 调用原始错误处理函数
                if (originalOnError) {
                    originalOnError(error, layerKey);
                }

                continue;
            }
        }

        // 所有层都失败了
        const elapsed = Date.now() - requestState.startTime;
        console.error(`❌ [${requestState.requestId}] 所有视频源加载失败 (${elapsed}ms, ${requestState.totalAttempts}次尝试)`);

        // 最后的降级：显示主题背景
        console.log(`🛡️ [${requestState.requestId}] 启用最终降级方案`);
        return await this.loadStarryBackground(pageName, videoElement, options);
    }

    /**
     * 生成指定层的视频URL
     * @param {string} pageName - 页面名称
     * @param {string} layerKey - 层级键名
     * @returns {string|null} 视频URL
     */
    generateVideoUrl(pageName, layerKey) {
        try {
            switch (layerKey) {
                case 'primary': // Cloudflare R2
                    return this.config.urls?.r2?.[pageName] || null;
                    
                case 'secondary': // Cloudinary
                    return this.generateCloudinaryUrl(pageName);
                    
                case 'tertiary': // VPS
                    return this.config.urls?.vps?.[pageName] || null;
                    
                case 'quaternary': // 星空背景 (CSS)
                    return this.config.urls?.starryBackground?.[pageName] || null;
                    
                default:
                    console.warn(`未知的层级: ${layerKey}`);
                    return null;
            }
        } catch (error) {
            console.error(`URL生成失败 (${layerKey}):`, error);
            return null;
        }
    }

    /**
     * 生成Cloudinary URL
     * @param {string} pageName - 页面名称
     * @returns {string|null} Cloudinary URL
     */
    generateCloudinaryUrl(pageName) {
        try {
            // 页面名称到账户键的映射
            const pageToAccountKey = {
                'home': 'INDEX',
                'anniversary': 'ANNIVERSARY', 
                'meetings': 'MEETINGS',
                'memorial': 'MEMORIAL',
                'together-days': 'TOGETHER_DAYS'
            };
            
            const accountKey = pageToAccountKey[pageName];
            if (!accountKey) {
                console.warn(`未找到页面 ${pageName} 的账户映射`);
                return null;
            }
            
            const accounts = this.config.layers.secondary?.accounts;
            if (!accounts || !accounts[accountKey]) {
                console.warn(`未找到账户配置: ${accountKey}`);
                return null;
            }
            
            const cloudName = accounts[accountKey].cloudName;
            return `https://res.cloudinary.com/${cloudName}/video/upload/love-website/${pageName}.mp4`;
            
        } catch (error) {
            console.error('Cloudinary URL生成失败:', error);
            return null;
        }
    }

    /**
     * 带超时的视频加载
     * @param {string} url - 视频URL
     * @param {HTMLVideoElement} videoElement - 视频元素
     * @param {number} timeout - 超时时间(毫秒)
     * @param {Object} options - 选项
     * @returns {Promise<boolean>} 是否加载成功
     */
    loadWithTimeout(url, videoElement, timeout, options = {}) {
        return new Promise((resolve, reject) => {
            const timer = setTimeout(() => {
                this.cleanupVideoListeners(videoElement);
                reject(new Error(`加载超时 (${timeout}ms)`));
            }, timeout);

            // 设置事件监听器
            const onLoadedData = () => {
                clearTimeout(timer);
                this.cleanupVideoListeners(videoElement);
                resolve(true);
            };

            const onError = (event) => {
                clearTimeout(timer);
                this.cleanupVideoListeners(videoElement);
                reject(new Error(`视频加载错误: ${event.target?.error?.message || '未知错误'}`));
            };

            const onProgress = () => {
                if (options.onProgress && videoElement.buffered.length > 0) {
                    const buffered = videoElement.buffered.end(0);
                    const duration = videoElement.duration || 0;
                    if (duration > 0) {
                        const progress = (buffered / duration) * 100;
                        options.onProgress(progress, options.layerInfo);
                    }
                }
            };

            // 绑定事件
            videoElement.addEventListener('loadeddata', onLoadedData, { once: true });
            videoElement.addEventListener('error', onError, { once: true });
            videoElement.addEventListener('progress', onProgress);

            // 存储监听器引用以便清理
            videoElement._videoLoaderListeners = { onLoadedData, onError, onProgress };

            // 设置视频源
            console.log(`🔗 加载URL: ${url}`);
            videoElement.src = url;
        });
    }

    /**
     * 清理视频事件监听器
     * @param {HTMLVideoElement} videoElement - 视频元素
     */
    cleanupVideoListeners(videoElement) {
        if (videoElement._videoLoaderListeners) {
            const { onLoadedData, onError, onProgress } = videoElement._videoLoaderListeners;
            videoElement.removeEventListener('loadeddata', onLoadedData);
            videoElement.removeEventListener('error', onError);
            videoElement.removeEventListener('progress', onProgress);
            delete videoElement._videoLoaderListeners;
        }
    }

    /**
     * 加载星空背景保障机制 - 第四层CDN降级保障
     * @param {string} pageName - 页面名称
     * @param {HTMLVideoElement} videoElement - 视频元素
     * @param {Object} options - 选项
     * @returns {Promise<boolean>} 总是返回true
     */
    async loadStarryBackground(pageName, videoElement, options = {}) {
        console.log(`✨ 启用第四层星空背景保障: ${pageName}`);

        try {
            // 确保星空背景CSS已加载
            await this.ensureStarryBackgroundCSS();

            // 隐藏视频元素
            videoElement.style.display = 'none';
            videoElement.style.opacity = '0';

            // 获取视频容器
            const videoContainer = videoElement.closest('.video-background') ||
                                 videoElement.parentElement ||
                                 document.body;

            if (videoContainer) {
                // 移除可能存在的视频相关类
                videoContainer.classList.remove('video-loaded', 'video-loading');

                // 添加星空背景类
                videoContainer.classList.add('starry-fallback', 'starry-background', pageName);

                // 应用主题背景作为基础
                const themeBackground = this.themeBackgrounds[pageName] || this.themeBackgrounds.home;
                videoContainer.style.background = themeBackground;

                // 添加星空背景加载指示器
                this.addStarryLoadingIndicator(videoContainer, pageName);

                // 延迟添加完全加载状态，创建平滑过渡效果
                setTimeout(() => {
                    videoContainer.classList.add('video-loaded');
                    this.removeStarryLoadingIndicator(videoContainer);
                }, 800);

                console.log(`🎨 应用${pageName}星空主题背景`);
            }

            // 添加页面级别的星空背景样式
            this.applyPageStarryStyles(pageName);

            // 调用成功回调
            if (options.onSuccess) {
                options.onSuccess('quaternary');
            }

            // 记录星空背景启用事件
            this.logStarryBackgroundEvent(pageName);

            return true;

        } catch (error) {
            console.error('星空背景设置失败:', error);

            // 即使失败也要提供基础的降级方案
            this.applyBasicFallback(videoElement, pageName);
            return true; // 确保流程继续
        }
    }

    /**
     * 确保星空背景CSS已加载
     * @returns {Promise<void>}
     */
    async ensureStarryBackgroundCSS() {
        // 检查是否已经加载了星空背景CSS
        const existingLink = document.querySelector('link[href*="starry-background.css"]');
        if (existingLink) {
            return; // 已经加载
        }

        return new Promise((resolve, reject) => {
            const link = document.createElement('link');
            link.rel = 'stylesheet';
            link.href = '/src/client/styles/starry-background.css';

            link.onload = () => {
                console.log('✅ 星空背景CSS加载成功');
                resolve();
            };

            link.onerror = () => {
                console.warn('⚠️ 星空背景CSS加载失败，使用内联样式');
                this.injectStarryBackgroundStyles();
                resolve(); // 即使失败也继续
            };

            document.head.appendChild(link);

            // 设置超时，避免无限等待
            setTimeout(() => {
                console.warn('⚠️ 星空背景CSS加载超时');
                resolve();
            }, 3000);
        });
    }

    /**
     * 注入内联星空背景样式（CSS加载失败时的降级方案）
     */
    injectStarryBackgroundStyles() {
        const styleId = 'starry-background-inline';
        if (document.getElementById(styleId)) {
            return; // 已经注入
        }

        const style = document.createElement('style');
        style.id = styleId;
        style.textContent = `
            .starry-fallback {
                position: relative;
                animation: starryFadeIn 1s ease-out;
            }
            .starry-fallback::before {
                content: '';
                position: absolute;
                top: 0; left: 0;
                width: 100%; height: 100%;
                background-image:
                    radial-gradient(2px 2px at 20px 30px, rgba(255,255,255,0.8), transparent),
                    radial-gradient(1px 1px at 40px 70px, rgba(255,255,255,0.6), transparent);
                background-repeat: repeat;
                background-size: 200px 100px;
                animation: starryMove 20s linear infinite;
                opacity: 0.6;
                z-index: 1;
            }
            .starry-fallback > * { position: relative; z-index: 2; }
            @keyframes starryMove {
                0% { transform: translateX(0) translateY(0); }
                100% { transform: translateX(-200px) translateY(-100px); }
            }
            @keyframes starryFadeIn {
                0% { opacity: 0; transform: scale(1.1); }
                100% { opacity: 1; transform: scale(1); }
            }
        `;
        document.head.appendChild(style);
        console.log('📝 注入内联星空背景样式');
    }

    /**
     * 添加星空背景加载指示器
     * @param {HTMLElement} container - 容器元素
     * @param {string} pageName - 页面名称
     */
    addStarryLoadingIndicator(container, pageName) {
        // 移除可能存在的旧指示器
        this.removeStarryLoadingIndicator(container);

        const indicator = document.createElement('div');
        indicator.className = 'starry-loading';
        indicator.innerHTML = `
            <div>✨</div>
            <div>正在启用${this.getPageDisplayName(pageName)}星空背景...</div>
        `;

        container.appendChild(indicator);
    }

    /**
     * 移除星空背景加载指示器
     * @param {HTMLElement} container - 容器元素
     */
    removeStarryLoadingIndicator(container) {
        const indicator = container.querySelector('.starry-loading');
        if (indicator) {
            indicator.remove();
        }
    }

    /**
     * 获取页面显示名称
     * @param {string} pageName - 页面名称
     * @returns {string} 显示名称
     */
    getPageDisplayName(pageName) {
        const displayNames = {
            'home': '首页',
            'anniversary': '纪念日',
            'meetings': '相遇记录',
            'memorial': '纪念页面',
            'together-days': '在一起的日子'
        };
        return displayNames[pageName] || pageName;
    }

    /**
     * 应用页面级别的星空背景样式
     * @param {string} pageName - 页面名称
     */
    applyPageStarryStyles(pageName) {
        // 为body添加星空背景类，确保全页面覆盖
        document.body.classList.add('starry-page', `starry-${pageName}`);

        // 设置页面元数据
        document.documentElement.setAttribute('data-starry-background', pageName);

        console.log(`🌟 应用页面级星空样式: ${pageName}`);
    }

    /**
     * 记录星空背景启用事件
     * @param {string} pageName - 页面名称
     */
    logStarryBackgroundEvent(pageName) {
        const event = {
            type: 'starry_background_activated',
            pageName: pageName,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent,
            loadingState: this.loadingState
        };

        // 存储到本地存储，用于调试和统计
        try {
            const events = JSON.parse(localStorage.getItem('love_starry_events') || '[]');
            events.push(event);

            // 只保留最近50个事件
            if (events.length > 50) {
                events.splice(0, events.length - 50);
            }

            localStorage.setItem('love_starry_events', JSON.stringify(events));
        } catch (error) {
            console.warn('星空背景事件记录失败:', error);
        }

        console.log('📊 星空背景事件已记录:', event);
    }

    /**
     * 应用基础降级方案（最后的保障）
     * @param {HTMLVideoElement} videoElement - 视频元素
     * @param {string} pageName - 页面名称
     */
    applyBasicFallback(videoElement, pageName) {
        try {
            videoElement.style.display = 'none';

            const container = videoElement.closest('.video-background') || videoElement.parentElement;
            if (container) {
                const themeBackground = this.themeBackgrounds[pageName] || this.themeBackgrounds.home;
                container.style.background = themeBackground;
                container.style.minHeight = '100vh';
                container.classList.add('basic-fallback');

                console.log(`🛡️ 应用基础降级方案: ${pageName}`);
            }
        } catch (error) {
            console.error('基础降级方案失败:', error);
        }
    }

    /**
     * 设置自定义加载顺序
     * @param {Array<string>} order - 加载顺序数组
     */
    setLoadingOrder(order) {
        if (Array.isArray(order) && order.length > 0) {
            this.loadingOrder = order;
            console.log(`🔄 更新加载顺序: ${order.join(' → ')}`);
        }
    }

    /**
     * 获取当前配置信息
     * @returns {Object} 配置信息
     */
    getConfig() {
        return {
            config: this.config,
            loadingOrder: this.loadingOrder,
            loadingState: this.loadingState,
            supportedPages: Object.keys(this.themeBackgrounds),
            themeBackgrounds: this.themeBackgrounds
        };
    }

    /**
     * 重置加载状态
     */
    reset() {
        this.loadingState = {
            initialized: this.loadingState.initialized,
            currentLayer: null,
            startTime: null,
            totalAttempts: 0
        };
    }

    /**
     * 页面集成辅助函数 - 替换现有页面的视频加载逻辑
     * @param {Object} options - 集成选项
     */
    static integrateWithPage(options = {}) {
        const {
            pageName = null,
            videoSelector = '.video-background video',
            loadingOverlaySelector = '#loadingOverlay',
            loadingProgressSelector = '#loadingProgress',
            autoDetectPage = true,
            customTheme = null
        } = options;

        // 自动检测页面名称
        const detectedPageName = pageName || VideoLoader.detectPageName();

        if (!detectedPageName) {
            console.error('❌ 无法检测页面名称，请手动指定');
            return;
        }

        console.log(`🔗 集成智能加载器到页面: ${detectedPageName}`);

        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                VideoLoader._performIntegration(detectedPageName, options);
            });
        } else {
            VideoLoader._performIntegration(detectedPageName, options);
        }
    }

    /**
     * 执行页面集成
     * @private
     */
    static _performIntegration(pageName, options) {
        const videoElement = document.querySelector(options.videoSelector || '.video-background video');
        const loadingOverlay = document.querySelector(options.loadingOverlaySelector || '#loadingOverlay');
        const loadingProgress = document.querySelector(options.loadingProgressSelector || '#loadingProgress');

        if (!videoElement) {
            console.error('❌ 未找到视频元素');
            return;
        }

        // 创建加载器实例
        const loader = window.videoLoader;

        // 设置加载选项
        const loadOptions = {
            onProgress: (progress, layerInfo) => {
                if (loadingProgress) {
                    loadingProgress.style.width = Math.min(progress, 90) + '%';
                }
                console.log(`📊 ${layerInfo?.key || 'unknown'} 加载进度: ${progress.toFixed(1)}%`);
            },
            onError: (error, layerKey) => {
                console.warn(`⚠️ ${layerKey} 层加载失败: ${error.message}`);
            },
            onSuccess: (layerKey) => {
                console.log(`✅ ${layerKey} 层加载成功`);

                // 隐藏加载遮罩
                if (loadingProgress) {
                    loadingProgress.style.width = '100%';
                }

                setTimeout(() => {
                    if (loadingOverlay) {
                        loadingOverlay.classList.add('hidden');
                    }
                }, 300);

                // 如果是视频加载成功，设置播放状态
                if (layerKey !== 'quaternary') {
                    videoElement.classList.add('loaded');
                    const videoContainer = videoElement.closest('.video-background');
                    if (videoContainer) {
                        videoContainer.classList.add('video-loaded');
                    }
                }
            }
        };

        // 开始智能加载
        loader.loadVideo(pageName, videoElement, loadOptions)
            .then(success => {
                if (success) {
                    console.log(`🎉 ${pageName} 页面视频加载完成`);

                    // 如果是视频成功加载，尝试播放
                    if (videoElement.style.display !== 'none') {
                        videoElement.play().catch(error => {
                            console.log('📱 视频自动播放被阻止:', error);
                        });
                    }
                } else {
                    console.error(`❌ ${pageName} 页面视频加载失败`);
                }
            })
            .catch(error => {
                console.error(`❌ ${pageName} 页面加载异常:`, error);
            });

        // 设置页面卸载清理
        window.addEventListener('beforeunload', () => {
            // 使用更可靠的方法检测F5刷新
            const isRefresh = window.performance && (
                window.performance.navigation?.type === 1 || // 标准方式
                window.performance.getEntriesByType('navigation')[0]?.type === 'reload' // 新标准
            );

            console.log('🔄 VideoLoader页面卸载事件，刷新检测:', isRefresh);

            if (videoElement && videoElement.style.display !== 'none') {
                if (isRefresh) {
                    console.log('🔄 检测到F5刷新，VideoLoader不清理视频资源');
                    // F5刷新时不清理，避免背景闪烁
                } else {
                    console.log('🧹 页面真正卸载，VideoLoader清理视频资源');
                    videoElement.pause();
                    videoElement.src = '';
                    videoElement.load();
                }
            }
        });

        // 设置页面可见性管理
        document.addEventListener('visibilitychange', () => {
            if (videoElement && videoElement.style.display !== 'none') {
                if (document.hidden) {
                    videoElement.pause();
                    console.log('📱 页面隐藏，暂停视频播放');
                } else {
                    videoElement.play().catch(error => {
                        console.log('📱 页面显示，恢复视频播放失败:', error);
                    });
                    console.log('📱 页面显示，恢复视频播放');
                }
            }
        });
    }

    /**
     * 自动检测当前页面名称
     * @returns {string|null} 页面名称
     */
    static detectPageName() {
        const path = window.location.pathname;

        // 路径映射
        const pathMapping = {
            '/': 'home',
            '/index.html': 'home',
            '/together-days': 'together-days',
            '/together-days.html': 'together-days',
            '/anniversary': 'anniversary',
            '/anniversary.html': 'anniversary',
            '/meetings': 'meetings',
            '/meetings.html': 'meetings',
            '/memorial': 'memorial',
            '/memorial.html': 'memorial'
        };

        return pathMapping[path] || null;
    }

    /**
     * 宏策略快速切换
     * @param {string} strategy - 策略名称
     */
    static async switchStrategy(strategy) {
        console.log(`🔄 切换视频加载策略: ${strategy}`);

        try {
            // 重新初始化配置
            window.videoLoader.config = null;

            // 临时设置环境变量 (仅用于测试)
            if (window.location.search.includes('debug=true')) {
                console.log(`🧪 测试模式: 使用策略 ${strategy}`);
            }

            await window.videoLoader.init();
            console.log(`✅ 策略切换完成: ${strategy}`);

        } catch (error) {
            console.error(`❌ 策略切换失败:`, error);
        }
    }
}

// 创建全局实例和工具函数
window.VideoLoader = VideoLoader;
window.videoLoader = new VideoLoader();

// 全局便捷函数
window.loadVideoWithSmartLoader = (pageName, options = {}) => {
    VideoLoader.integrateWithPage({ pageName, ...options });
};

window.switchVideoStrategy = VideoLoader.switchStrategy;

console.log('📦 智能视频加载器已加载 - 支持四层CDN降级架构');
