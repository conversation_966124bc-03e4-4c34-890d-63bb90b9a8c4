<!DOCTYPE html>
<html lang="zh-CN" style="background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%) !important; margin: 0; padding: 0;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="记录我们在一起的美好日子，每一天都是爱情的见证">
    <title>在一起的日子 - Yu 💕 Wang</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💕</text></svg>">

    <!-- 立即应用背景样式，防止白色闪烁 -->
    <style>
        html {
            background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%) !important;
            margin: 0 !important;
            padding: 0 !important;
            min-height: 100vh !important;
        }
        body {
            background: transparent !important;
            margin: 0 !important;
            padding: 0 !important;
            min-height: 100vh !important;
        }
    </style>

    <!-- 立即显示加载遮罩，防止白色闪烁 - 使用和主页相同的CSS系统 -->
    <style>
        /* 立即显示加载遮罩，防止看到背景 */
        .loading-overlay {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%) !important;
            display: flex !important;
            flex-direction: column !important;
            justify-content: center !important;
            align-items: center !important;
            z-index: 9999 !important;
            opacity: 1 !important;
            visibility: visible !important;
        }

        /* 使用CSS立即显示加载动画 */
        .loading-overlay::before {
            content: '' !important;
            width: 60px !important;
            height: 60px !important;
            border: 4px solid rgba(255, 255, 255, 0.3) !important;
            border-top: 4px solid white !important;
            border-radius: 50% !important;
            animation: spin 1s linear infinite !important;
            margin-bottom: 20px !important;
            display: block !important;
        }

        /* 使用CSS立即显示加载文字 */
        .loading-overlay::after {
            content: '🌊 Loading Together Days...' !important;
            white-space: pre-line !important;
            color: white !important;
            font-family: 'Dancing Script', 'Great Vibes', 'Courgette', cursive !important;
            font-size: 2.5rem !important;
            font-weight: 700 !important;
            text-align: center !important;
            line-height: 1.6 !important;
            display: block !important;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
            letter-spacing: 2px !important;
        }

        .loading-overlay.hidden {
            opacity: 0 !important;
            visibility: hidden !important;
            transition: opacity 0.8s ease-out, visibility 0.8s ease-out !important;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
    </style>

    <link rel="stylesheet" href="/src/client/styles/pages.css">
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 海底视频背景样式 */
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .video-background video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0; /* 初始透明，加载完成后显示 */
            filter: contrast(1.1); /* 保持原始亮度，仅增强对比度 */
            transition: opacity 1s ease-in-out; /* 平滑过渡效果 */
        }

        /* 视频加载完成后显示 */
        .video-background video.loaded {
            opacity: 1.0; /* 完全不透明，显示视频原始亮度 */
        }

        /* 视频加载时的备用背景 */
        .video-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                #0c0c2e 0%,
                #1a1a3e 25%,
                #2d1b69 50%,
                #4a148c 75%,
                #6a1b9a 100%);
            z-index: -1;
            transition: opacity 1s ease-in-out;
        }

        /* 视频加载完成后隐藏备用背景 */
        .video-background.video-loaded::before {
            opacity: 0;
        }

        /* 加载遮罩样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                #0c4a6e 0%,
                #075985 25%,
                #0369a1 50%,
                #0284c7 75%,
                #0ea5e9 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
        }

        .loading-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

        /* 加载动画 */
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: white;
            font-family: 'Dancing Script', cursive;
            font-size: 1.5rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 10px;
        }

        .loading-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-family: 'Inter', sans-serif;
            font-size: 1rem;
            text-align: center;
        }

        /* 加载进度条 */
        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            margin-top: 20px;
            overflow: hidden;
        }

        .loading-progress-bar {
            height: 100%;
            background: white;
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease;
            animation: progressPulse 2s ease-in-out infinite;
        }

        @keyframes progressPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* 覆盖原有的body背景，让视频背景显示 */
        body {
            background: transparent !important; /* 移除遮罩，让视频背景完全显示 */
        }

        /* 调整星空效果的透明度，与海底背景更好融合 */
        body::before {
            opacity: 0.3;
        }

        body::after {
            opacity: 0.2;
        }

        /* 管理功能样式 */
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .add-btn {
            /* 透明背景按钮 */
            background: transparent;
            color: rgba(72, 202, 228, 0.9);
            border: 2px solid rgba(72, 202, 228, 0.6);
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 10px rgba(72, 202, 228, 0.3);
        }

        .add-btn:hover {
            background: transparent;
            color: rgba(72, 202, 228, 1);
            border-color: rgba(72, 202, 228, 0.8);
            transform: translateY(-2px);
            box-shadow: 0 0 15px rgba(72, 202, 228, 0.5);
        }

        .timeline-actions {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .timeline-item:hover .timeline-actions {
            opacity: 1;
        }

        .timeline-item {
            position: relative;
        }

        .action-btn {
            width: 35px;
            height: 35px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            /* 移除磨砂效果，让背景完全透明 */
        }

        .edit-btn {
            background: rgba(52, 152, 219, 0.9);
            color: white;
        }

        .edit-btn:hover {
            background: rgba(52, 152, 219, 1);
            transform: scale(1.1);
        }

        .delete-btn {
            background: rgba(231, 76, 60, 0.9);
            color: white;
        }

        .delete-btn:hover {
            background: rgba(231, 76, 60, 1);
            transform: scale(1.1);
        }

        /* 增强内容区域的可读性 */
        .content-section {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
            border: 1px solid rgba(255, 255, 255, 0.2);
            /* 减少外层容器边距，让时光轴更接近页面边缘，增加底部空间 */
            padding: 40px 5px 80px 5px !important;
            /* 确保内容不会溢出 */
            overflow: visible !important;
        }

        .page-header {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
        }

        .timeline-item {
            /* 调整卡片间距更紧凑 */
            margin-bottom: -30px !important;
            padding: 5px 25px !important;
        }
        
        .timeline-item::after {
            /* 圆点动态居中对齐卡片内容 */
            top: 50% !important;
            transform: translateY(-50%) !important;
        }
        
        .timeline-content {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
            /* 字体离卡片边缘更近 */
            padding: 15px 20px !important;
            /* 减少卡片高度以紧凑排列 */
            min-height: 160px !important;
        }
        
        .timeline-header {
            /* 标题和日期紧贴显示，为右侧按钮留空间 */
            display: flex !important;
            align-items: center !important;
            margin-bottom: 15px !important;
            flex-wrap: wrap !important;
        }
        
        .timeline-title {
            /* 标题样式调整 */
            margin: 0 !important;
            margin-right: 8px !important;
        }
        
        .timeline-separator {
            /* 爱心分隔符样式 */
            margin: 0 5px !important;
            font-size: 0.9em !important;
        }
        
        .timeline-date {
            /* 日期样式调整 */
            margin: 0 !important;
            white-space: nowrap !important;
            font-size: 0.85rem !important;
        }
        
        /* 嵌入式按钮样式 - 定位到右上角 */
        .embedded-actions {
            position: absolute !important;
            top: 15px !important;
            right: 15px !important;
            display: flex !important;
            gap: 8px !important;
            opacity: 0 !important;
            transform: translateY(-10px) !important;
            transition: all 0.3s ease !important;
        }
        
        .timeline-content:hover .embedded-actions {
            opacity: 1 !important;
            transform: translateY(0) !important;
        }
        
        .timeline-content .embedded-btn {
            background: transparent !important;
            background-color: transparent !important;
            background-image: none !important;
            border: 1px solid rgba(255, 255, 255, 0.6) !important;
            color: #666 !important;
            padding: 4px 8px !important;
            border-radius: 12px !important;
            font-size: 0.75rem !important;
            cursor: pointer !important;
            transition: opacity 0.3s ease, color 0.3s ease, border-color 0.3s ease !important;
            display: flex !important;
            align-items: center !important;
            gap: 3px !important;
            backdrop-filter: none !important;
            box-shadow: none !important;
        }
        
        .timeline-content .embedded-btn.edit-btn:hover {
            background: transparent !important;
            background-color: transparent !important;
            border-color: #48cae4 !important;
            color: #0077b6 !important;
        }
        
        .timeline-content .embedded-btn.delete-btn:hover {
            background: transparent !important;
            background-color: transparent !important;
            border-color: #ff6b6b !important;
            color: #ee5a52 !important;
        }

        .memory-card {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
        }

        .quote-section {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
        }

        /* 爱情感悟区域样式 */
        .quote-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
            flex-wrap: wrap;
            gap: 15px;
        }

        .quote-refresh-btn {
            /* 透明背景按钮 */
            background: transparent;
            color: rgba(72, 202, 228, 0.9);
            border: 2px solid rgba(72, 202, 228, 0.6);
            padding: 10px 20px;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 10px rgba(72, 202, 228, 0.3);
            font-family: 'ZiXiaoHunGouYu', 'Dancing Script', cursive;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .quote-refresh-btn:hover {
            background: transparent;
            color: rgba(72, 202, 228, 1);
            border-color: rgba(72, 202, 228, 0.8);
            transform: translateY(-2px);
            box-shadow: 0 0 15px rgba(72, 202, 228, 0.5);
        }

        .quote-refresh-btn i {
            transition: transform 0.3s ease;
        }

        .quote-refresh-btn:hover i {
            transform: rotate(180deg);
        }

        /* 引用文本动画效果 */
        .quote-text {
            transition: all 0.5s ease;
            opacity: 1;
        }

        .quote-text.changing {
            opacity: 0;
            transform: translateY(10px);
        }

        .quote-author {
            transition: all 0.5s ease;
            opacity: 1;
        }

        .quote-author.changing {
            opacity: 0;
            transform: translateY(10px);
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            display: flex;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
            animation: modalFadeIn 0.4s ease-out;
        }

        @keyframes modalFadeIn {
            from {
                opacity: 0;
                backdrop-filter: blur(0px);
            }
            to {
                opacity: 1;
                backdrop-filter: blur(5px);
            }
        }

        .modal-content {
            /* 完全透明背景 - 参考首页星星话语 */
            background: transparent;
            border-radius: 25px;
            padding: 0;
            max-width: 600px;
            width: 90%;
            max-height: 80vh;
            overflow: visible;
            /* 淡蓝色发光边框效果 */
            box-shadow:
                0 0 30px rgba(72, 202, 228, 0.3),
                0 0 60px rgba(72, 202, 228, 0.2),
                0 0 90px rgba(72, 202, 228, 0.1);
            border: 2px solid rgba(72, 202, 228, 0.4);
            animation: modalAppear 0.5s ease-out;
            transform-origin: center;
            position: relative;
        }
        
        /* 复制首页的modalAppear动画 */
        @keyframes modalAppear {
            from {
                opacity: 0;
                transform: scale(0.8) translateY(-50px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: scale(0.7) translateY(-50px);
            }
            to {
                opacity: 1;
                transform: scale(1) translateY(0);
            }
        }

        .modal-header {
            padding: 25px 30px 15px;
            border-bottom: 1px solid rgba(148, 163, 184, 0.2);
            display: flex;
            justify-content: space-between;
            align-items: center;
            /* 透明背景 - 参考首页 */
            background: transparent;
            border-radius: 25px 25px 0 0;
            position: relative;
            z-index: 2;
        }

        .modal-header h3 {
            /* 复制首页星星话语标题样式 */
            font-family: 'Dancing Script', cursive;
            font-size: 1.8rem;
            color: #334155;
            margin: 0;
            text-shadow: 0 1px 2px rgba(255, 255, 255, 0.8);
        }

        .modal-close {
            /* 复制首页关闭按钮样式 */
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #666;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .modal-close:hover {
            background: rgba(255, 255, 255, 0.5);
            color: #333;
            transform: rotate(90deg);
        }

        .modal-form {
            /* 复制首页内容区域样式 */
            padding: 40px;
            text-align: center;
            animation: fadeInContent 0.6s ease-out;
            position: relative;
            overflow: visible;
            /* 透明背景 */
            background: transparent;
            /* 淡蓝色发光边框效果 */
            border: 2px solid rgba(72, 202, 228, 0.4);
            border-radius: 20px;
            box-shadow:
                0 0 20px rgba(72, 202, 228, 0.3),
                0 0 40px rgba(72, 202, 228, 0.2),
                0 0 60px rgba(72, 202, 228, 0.1),
                inset 0 0 20px rgba(72, 202, 228, 0.05);
            z-index: 2;
        }
        
        /* 添加鲸鱼装饰 */
        .modal-form::after {
            content: '🐋';
            position: absolute;
            top: 15px;
            right: 20px;
            font-size: 1.2rem;
            opacity: 0.8;
            animation: sparkle 3s ease-in-out infinite;
            text-shadow: 0 0 10px rgba(72, 202, 228, 0.5);
        }
        
        @keyframes fadeInContent {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes sparkle {
            0%, 100% {
                opacity: 0.6;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.2);
            }
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-family: 'Dancing Script', cursive;
            font-size: 1.2rem;
            font-weight: 600;
            /* 淡蓝色标签 */
            color: rgba(72, 202, 228, 0.9);
        }

        /* 添加字体引用 */
        @font-face {
            font-family: 'ZiXiaoHunSanFen';
            src: url('/fonts/字小魂三分行楷(商用需授权).ttf') format('truetype');
            font-display: swap;
        }

        @font-face {
            font-family: 'ZiXiaoHunGouYu';
            src: url('/fonts/字小魂勾玉行书(商用需授权).ttf') format('truetype');
            font-display: swap;
        }

        @font-face {
            font-family: 'ZiHunXingYunFeiBai';
            src: url('/fonts/字魂行云飞白体(商用需授权).ttf') format('truetype');
            font-display: swap;
        }
        
        /* 标题类文本使用勾玉行书字体 */
        .page-title,
        .section-title, 
        .add-btn,
        .timeline-title,
        .memory-card-title {
            font-family: 'ZiXiaoHunGouYu', 'Dancing Script', cursive !important;
        }
        
        /* 内容文本使用三分行楷字体，淡青色 */
        .page-subtitle,
        .timeline-description,
        .memory-card-content,
        .romantic-text {
            font-family: 'ZiXiaoHunSanFen', 'Inter', sans-serif !important;
            color: #67e8f9 !important;
        }
        
        /* 对未来的憧憬标题增大字体 */
        .future-dreams .section-title {
            font-size: 2.2rem !important;
        }
        
        /* 爱情感悟部分使用行云飞白体 */
        .quote-text,
        .quote-author {
            font-family: 'ZiHunXingYunFeiBai', 'Dancing Script', cursive !important;
        }
        
        /* 标题输入组样式 */
        .title-input-group {
            display: flex;
            flex-direction: column;
            gap: 8px;
        }
        
        .emoji-selector {
            display: flex;
            gap: 5px;
            flex-wrap: wrap;
            justify-content: center;
            margin-bottom: 10px;
        }
        
        .emoji-btn {
            background: transparent;
            border: 1px solid rgba(72, 202, 228, 0.4);
            border-radius: 8px;
            padding: 6px;
            font-size: 1.2rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 5px rgba(72, 202, 228, 0.2);
        }
        
        .emoji-btn:hover {
            background: transparent;
            border-color: rgba(72, 202, 228, 0.6);
            transform: scale(1.1);
            box-shadow: 0 0 10px rgba(72, 202, 228, 0.4);
        }
        
        /* 输入框与按钮容器 */
        .input-with-button-container {
            position: relative;
            display: flex;
            align-items: center;
        }
        
        .input-with-button-container input,
        .date-display-input {
            flex: 1;
            padding-right: 50px; /* 为右侧按钮留出空间 */
        }
        
        .date-display-input {
            width: 100%;
            padding: 12px 50px 12px 15px;
            background: transparent;
            border: 2px solid rgba(72, 202, 228, 0.5);
            border-radius: 10px;
            font-family: 'ZiXiaoHunSanFen', 'Inter', sans-serif;
            font-size: 1rem;
            color: #22d3ee;
            box-shadow: 
                0 0 15px rgba(72, 202, 228, 0.3),
                0 0 30px rgba(72, 202, 228, 0.2),
                0 0 45px rgba(72, 202, 228, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
            display: flex;
            align-items: center;
        }
        
        .date-display-input:hover {
            border-color: rgba(72, 202, 228, 0.7);
            box-shadow: 
                0 0 20px rgba(72, 202, 228, 0.4),
                0 0 40px rgba(72, 202, 228, 0.3),
                0 0 60px rgba(72, 202, 228, 0.2);
        }
        
        /* 嵌入式按钮通用样式 */
        .embedded-emoji-btn,
        .embedded-date-btn {
            position: absolute;
            right: 8px;
            top: 50%;
            transform: translateY(-50%);
            background: transparent;
            border: none;
            font-size: 1.4rem;
            cursor: pointer;
            transition: all 0.3s ease;
            padding: 6px;
            border-radius: 8px;
            box-shadow: 0 0 8px rgba(72, 202, 228, 0.4);
            z-index: 10;
        }
        
        .embedded-emoji-btn:hover,
        .embedded-date-btn:hover {
            transform: translateY(-50%) scale(1.1);
            box-shadow: 0 0 15px rgba(72, 202, 228, 0.6);
        }
        
        /* emoji选择器容器调整 */
        .emoji-selector-container {
            position: absolute;
            right: 0;
            top: 0;
            height: 100%;
            display: flex;
            align-items: center;
        }
        
        .emoji-dropdown {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.8);
            width: 360px;
            max-height: 80vh;
            background: transparent;
            border: 2px solid rgba(72, 202, 228, 0.6);
            border-radius: 15px;
            padding: 20px;
            z-index: 10000;
            box-shadow: 
                0 0 30px rgba(72, 202, 228, 0.4),
                0 0 60px rgba(72, 202, 228, 0.3),
                0 0 90px rgba(72, 202, 228, 0.2);
            backdrop-filter: blur(10px);
            overflow-y: auto;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease-out;
        }
        
        .emoji-dropdown.show {
            opacity: 1;
            visibility: visible;
            transform: translate(-50%, -50%) scale(1);
        }
        
        /* 自定义发光滚动条 */
        .emoji-dropdown::-webkit-scrollbar {
            width: 8px;
        }
        
        .emoji-dropdown::-webkit-scrollbar-track {
            background: rgba(0, 0, 0, 0.1);
            border-radius: 4px;
            box-shadow: inset 0 0 5px rgba(72, 202, 228, 0.2);
        }
        
        .emoji-dropdown::-webkit-scrollbar-thumb {
            background: linear-gradient(180deg, 
                rgba(72, 202, 228, 0.8) 0%, 
                rgba(34, 211, 238, 0.8) 50%, 
                rgba(72, 202, 228, 0.8) 100%);
            border-radius: 4px;
            box-shadow: 
                0 0 10px rgba(72, 202, 228, 0.6),
                0 0 20px rgba(72, 202, 228, 0.4),
                inset 0 0 5px rgba(255, 255, 255, 0.2);
        }
        
        .emoji-dropdown::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(180deg, 
                rgba(72, 202, 228, 1.0) 0%, 
                rgba(34, 211, 238, 1.0) 50%, 
                rgba(72, 202, 228, 1.0) 100%);
            box-shadow: 
                0 0 15px rgba(72, 202, 228, 0.8),
                0 0 30px rgba(72, 202, 228, 0.6),
                inset 0 0 8px rgba(255, 255, 255, 0.3);
        }
        
        /* 添加弹窗背景遮罩 */
        .emoji-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.3);
            z-index: 9999;
            backdrop-filter: blur(5px);
            display: none;
        }
        
        .emoji-backdrop.show {
            display: block;
            animation: backdropFadeIn 0.3s ease-out;
        }
        
        @keyframes backdropFadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        

        
        .emoji-category {
            margin-bottom: 15px;
        }
        
        .emoji-category-title {
            font-family: 'ZiXiaoHunGouYu', 'Dancing Script', cursive;
            color: #22d3ee;
            font-size: 0.9rem;
            margin-bottom: 8px;
            text-align: center;
        }
        
        .emoji-grid {
            display: grid;
            grid-template-columns: repeat(6, 1fr);
            gap: 5px;
        }
        
        .emoji-option {
            background: transparent;
            border: 1px solid rgba(72, 202, 228, 0.3);
            border-radius: 8px;
            padding: 8px;
            font-size: 1.3rem;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            aspect-ratio: 1;
        }
        
        .emoji-option:hover {
            border-color: rgba(72, 202, 228, 0.6);
            transform: scale(1.2);
            box-shadow: 0 0 12px rgba(72, 202, 228, 0.4);
        }
        

        
        /* 自定义日期选择器弹窗 */
        .date-picker-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.6);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 10000;
            backdrop-filter: blur(10px);
        }
        
        .date-picker-modal {
            background: transparent;
            border: 2px solid rgba(72, 202, 228, 0.6);
            border-radius: 20px;
            padding: 20px;
            box-shadow: 
                0 0 30px rgba(72, 202, 228, 0.4),
                0 0 60px rgba(72, 202, 228, 0.3),
                0 0 90px rgba(72, 202, 228, 0.2);
            animation: modalAppear 0.3s ease-out;
        }
        
        .date-picker-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 20px;
            color: #22d3ee;
            font-family: 'ZiXiaoHunGouYu', 'Dancing Script', cursive;
        }
        
        .date-picker-nav {
            background: transparent;
            border: 1px solid rgba(72, 202, 228, 0.5);
            border-radius: 6px;
            padding: 5px 10px;
            color: #22d3ee;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 0 8px rgba(72, 202, 228, 0.3);
        }
        
        .date-picker-nav:hover {
            border-color: rgba(72, 202, 228, 0.7);
            box-shadow: 0 0 12px rgba(72, 202, 228, 0.5);
            transform: scale(1.05);
        }
        
        .date-picker-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: 5px;
            min-width: 300px;
        }
        
        .date-picker-day {
            aspect-ratio: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background: transparent;
            border: 1px solid rgba(72, 202, 228, 0.3);
            border-radius: 8px;
            color: #22d3ee;
            cursor: pointer;
            transition: all 0.3s ease;
            font-family: 'ZiXiaoHunSanFen', 'Inter', sans-serif;
            box-shadow: 0 0 5px rgba(72, 202, 228, 0.2);
        }
        
        .date-picker-day:hover {
            border-color: rgba(72, 202, 228, 0.6);
            box-shadow: 0 0 10px rgba(72, 202, 228, 0.4);
            transform: scale(1.1);
        }
        
        .date-picker-day.selected {
            border-color: rgba(72, 202, 228, 1.0) !important;
            background: rgba(72, 202, 228, 0.3) !important;
            color: #ffffff !important;
            font-weight: bold !important;
            box-shadow: 
                0 0 20px rgba(72, 202, 228, 0.8),
                0 0 40px rgba(72, 202, 228, 0.6),
                inset 0 0 10px rgba(72, 202, 228, 0.4) !important;
            transform: scale(1.1) !important;
        }
        
        .date-picker-day.today {
            border-color: rgba(34, 211, 238, 0.8);
            background: rgba(34, 211, 238, 0.1);
            font-weight: bold;
            box-shadow: 0 0 10px rgba(34, 211, 238, 0.5);
        }
        
        .date-picker-day.today.selected {
            border-color: rgba(72, 202, 228, 1.0) !important;
            background: rgba(72, 202, 228, 0.4) !important;
            box-shadow: 
                0 0 25px rgba(72, 202, 228, 1.0),
                0 0 50px rgba(72, 202, 228, 0.8),
                inset 0 0 15px rgba(72, 202, 228, 0.6) !important;
        }
        
        .date-picker-day.other-month {
            opacity: 0.4;
        }
        
        .date-picker-day.future {
            opacity: 0.3;
            cursor: not-allowed;
            color: #666 !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            box-shadow: none !important;
        }
        
        .date-picker-day.future:hover {
            background: transparent !important;
            border-color: rgba(255, 255, 255, 0.2) !important;
            transform: none !important;
            box-shadow: none !important;
        }
        
        .form-group input,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            /* 透明背景 - 参考首页星星话语 */
            background: transparent;
            /* 增强的淡蓝色发光边框效果 */
            border: 2px solid rgba(72, 202, 228, 0.5);
            border-radius: 10px;
            /* 使用指定字体 */
            font-family: 'ZiXiaoHunSanFen', 'Inter', sans-serif;
            font-size: 1rem;
            /* 淡青色字体 */
            color: #22d3ee;
            /* 增强的淡蓝色发光效果 */
            box-shadow: 
                0 0 15px rgba(72, 202, 228, 0.3),
                0 0 30px rgba(72, 202, 228, 0.2),
                0 0 45px rgba(72, 202, 228, 0.1);
            transition: all 0.3s ease;
        }
        
        /* 日期输入框特殊样式 */
        .form-group input[type="date"] {
            /* 增强日期选择器的发光效果 */
            box-shadow: 
                0 0 15px rgba(72, 202, 228, 0.4),
                0 0 30px rgba(72, 202, 228, 0.2),
                0 0 45px rgba(72, 202, 228, 0.1);
            border: 2px solid rgba(72, 202, 228, 0.6);
        }
        
        /* 日期选择器图标美化 */
        .form-group input[type="date"]::-webkit-calendar-picker-indicator {
            background: transparent;
            cursor: pointer;
            filter: brightness(0) saturate(100%) invert(69%) sepia(49%) saturate(2194%) hue-rotate(165deg) brightness(107%) contrast(99%);
            opacity: 0.8;
            transition: all 0.3s ease;
        }
        
        .form-group input[type="date"]::-webkit-calendar-picker-indicator:hover {
            opacity: 1;
            transform: scale(1.1);
        }

        .form-group input:focus,
        .form-group textarea:focus {
            outline: none;
            background: transparent;
            border-color: rgba(72, 202, 228, 0.6);
            box-shadow: 
                0 0 15px rgba(72, 202, 228, 0.3),
                0 0 30px rgba(72, 202, 228, 0.2),
                0 0 0 3px rgba(72, 202, 228, 0.1);
        }
        
        /* 日期选择器聚焦时的增强发光 */
        .form-group input[type="date"]:focus {
            border-color: rgba(72, 202, 228, 0.8);
            box-shadow: 
                0 0 20px rgba(72, 202, 228, 0.5),
                0 0 40px rgba(72, 202, 228, 0.3),
                0 0 60px rgba(72, 202, 228, 0.2),
                0 0 0 3px rgba(72, 202, 228, 0.15);
        }

        .form-group textarea {
            min-height: 100px;
            resize: vertical;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .btn-cancel,
        .btn-submit {
            padding: 12px 25px;
            /* 透明背景 */
            background: transparent;
            border-radius: 25px;
            font-family: 'Dancing Script', cursive;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .btn-cancel {
            /* 淡蓝色边框和字体 */
            color: rgba(72, 202, 228, 0.8);
            border: 2px solid rgba(72, 202, 228, 0.6);
            box-shadow: 0 0 10px rgba(72, 202, 228, 0.2);
        }

        .btn-cancel:hover {
            background: transparent;
            color: rgba(72, 202, 228, 1);
            border-color: rgba(72, 202, 228, 0.8);
            transform: translateY(-2px);
            box-shadow: 0 0 15px rgba(72, 202, 228, 0.4);
        }

        .btn-submit {
            /* 淡青色边框和字体 */
            color: #22d3ee;
            border: 2px solid rgba(34, 211, 238, 0.6);
            box-shadow: 0 0 10px rgba(34, 211, 238, 0.3);
        }

        .btn-submit:hover {
            background: transparent;
            color: rgba(34, 211, 238, 1);
            border-color: rgba(34, 211, 238, 0.8);
            transform: translateY(-2px);
            box-shadow: 0 0 15px rgba(34, 211, 238, 0.4);
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 30px;
            right: 30px;
            background: linear-gradient(135deg, #48cae4 0%, #0077b6 100%);
            color: white;
            padding: 15px 25px;
            border-radius: 25px;
            font-family: 'Dancing Script', cursive;
            font-size: 1.1rem;
            font-weight: 600;
            box-shadow: 0 10px 30px rgba(72, 202, 228, 0.3);
            z-index: 10001;
            display: flex;
            align-items: center;
            gap: 10px;
            opacity: 0;
            transform: translateX(100px);
            transition: all 0.4s ease;
        }

        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification-success {
            background: linear-gradient(135deg, #48cae4 0%, #0077b6 100%);
        }

        .notification-error {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        }

        .notification i {
            font-size: 1.2rem;
        }

        /* 响应式调整 */
        @media (max-width: 768px) {
            .modal-content {
                width: 95%;
                margin: 20px;
            }

            .modal-header,
            .modal-form {
                padding: 20px;
            }

            .form-actions {
                flex-direction: column;
            }

            .notification {
                right: 15px;
                left: 15px;
                text-align: center;
            }
        }

        /* 未来憧憬特殊样式 */
        .future-dreams {
            background: radial-gradient(ellipse at center,
                rgba(255, 240, 245, 0.95) 0%,
                rgba(255, 250, 240, 0.9) 30%,
                rgba(240, 248, 255, 0.85) 100%);
            border: 3px solid;
            border-image: linear-gradient(45deg, 
                #ff6b6b 0%, 
                #feca57 25%, 
                #48cae4 50%, 
                #a8e6cf 75%, 
                #ff6b6b 100%) 1;
            box-shadow: 0 20px 60px rgba(255, 105, 180, 0.2),
                        0 0 40px rgba(72, 202, 228, 0.1),
                        inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }

        .future-content {
            text-align: center;
            padding: 15px 0;
        }

        .romantic-text {
            font-family: 'Dancing Script', 'Playfair Display', cursive;
            font-size: 1.6rem;
            font-weight: 600;
            line-height: 2.2;
            background: linear-gradient(135deg, 
                #ff6b6b 0%, 
                #feca57 25%, 
                #48cae4 50%, 
                #a8e6cf 75%, 
                #ff6b6b 100%);
            background-size: 200% 200%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textShimmer 4s ease-in-out infinite;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
            letter-spacing: 0.5px;
        }

        @keyframes textShimmer {
            0% {
                background-position: 0% 50%;
            }
            50% {
                background-position: 100% 50%;
            }
            100% {
                background-position: 0% 50%;
            }
        }

        .text-divider {
            margin: 25px 0;
            text-align: center;
            position: relative;
        }

        .text-divider::before,
        .text-divider::after {
            content: '';
            position: absolute;
            top: 50%;
            width: 30%;
            height: 2px;
            background: linear-gradient(90deg, 
                transparent 0%, 
                #ff6b6b 30%, 
                #feca57 50%, 
                #48cae4 70%, 
                transparent 100%);
        }

        .text-divider::before {
            left: 0;
        }

        .text-divider::after {
            right: 0;
        }

        .heart-divider {
            font-size: 2rem;
            background: linear-gradient(135deg, #ff6b6b 0%, #feca57 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: heartBeat 2s ease-in-out infinite;
            display: inline-block;
            padding: 0 20px;
            background-color: rgba(255, 255, 255, 0.9);
            border-radius: 20px;
        }

        @keyframes heartBeat {
            0% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.1);
            }
            100% {
                transform: scale(1);
            }
        }

        /* 移动端适配 */
        @media (max-width: 768px) {
            .romantic-text {
                font-size: 1.4rem;
                line-height: 2;
            }
        }
    </style>
</head>
<body>
    <!-- 加载遮罩 - 使用和主页相同的CSS系统 -->
    <div class="loading-overlay" id="loadingOverlay"></div>

    <!-- 原有加载遮罩保留用于兼容性 -->
    <div id="oldLoadingOverlay" style="display: none;">
        <div class="loading-spinner"></div>
        <div class="loading-text">🌊 加载海底背景中...</div>
        <div class="loading-subtitle">正在为您准备美好的爱情时光</div>
        <div class="loading-progress">
            <div class="loading-progress-bar" id="loadingProgress"></div>
        </div>
    </div>

    <!-- 海底视频背景 -->
    <div class="video-background">
        <video autoplay muted loop playsinline preload="metadata">
            <source src="/src/client/assets/video-compressed/together-days.mp4" type="video/mp4">
            <!-- 如果浏览器不支持视频，显示备用背景 -->
            您的浏览器不支持视频播放。
        </video>
    </div>

    <!-- 流星效果 -->
    <div class="shooting-stars">
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
    </div>

    <!-- 浪漫漂浮元素 -->
    <div class="hearts-container">
        <div class="heart type-1 small"></div>
        <div class="heart type-2 medium"></div>
        <div class="heart type-3 large"></div>
        <div class="heart type-4 small"></div>
        <div class="heart type-5 medium"></div>
        <div class="heart type-6 large"></div>
        <div class="heart type-7 small"></div>
        <div class="heart type-8 medium"></div>
        <div class="heart type-9 large"></div>
        <div class="heart type-10 small"></div>
        <div class="heart type-11 medium"></div>
        <div class="heart type-12 large"></div>
        <div class="heart type-1 medium"></div>
        <div class="heart type-3 small"></div>
        <div class="heart type-5 large"></div>
    </div>

    <div class="container">
        <!-- 返回按钮 -->
        <a href="javascript:void(0)" onclick="window.location.href = getHomeUrl()" class="back-button">
            <i class="fas fa-arrow-left"></i>
            返回主页
        </a>

        <!-- 页面头部 -->
        <header class="page-header">
            <i class="fas fa-calendar-heart page-icon"></i>
            <h1 class="page-title">在一起的日子</h1>
            <p class="page-subtitle">每一天都是爱情的见证，每一刻都值得珍藏</p>
        </header>

        <!-- 恋爱时光轴 -->
        <section class="content-section">
            <div class="section-header">
                <h2 class="section-title">我们的爱情时光轴</h2>
                <button class="add-btn" onclick="window.openAddTimelineModal()">
                    <i class="fas fa-plus"></i>
                    添加时刻
                </button>
            </div>
            <div class="timeline">
                <!-- 时光轴内容将由JavaScript动态生成 -->
            </div>
        </section>

        <!-- 美好瞬间 -->
        <section class="content-section">
            <div class="section-header">
                <h2 class="section-title">那些美好的瞬间</h2>
                <button class="add-btn" onclick="window.openAddMemoryModal()">
                    <i class="fas fa-plus"></i>
                    添加瞬间
                </button>
            </div>
            <div class="memory-grid">
                <!-- 美好瞬间内容将由JavaScript动态生成 -->
            </div>
        </section>

        <!-- 爱情感悟 -->
        <section class="quote-section">
            <div class="quote-header">
                <h2 class="section-title">我们的爱情感悟</h2>
                <button class="quote-refresh-btn" onclick="refreshQuote()" title="换一句美好的话语">
                    <i class="fas fa-sync-alt"></i>
                    换一句
                </button>
            </div>
            <p class="quote-text" id="currentQuote">
                "爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏一个不完美的人。
                在一起的每一天，都让我更加确信，你就是我要找的那个人。"
            </p>
            <p class="quote-author" id="currentQuoteAuthor">— 我们的爱情感悟</p>
        </section>

        <!-- 未来展望 -->
        <section class="content-section future-dreams">
            <h2 class="section-title">对未来的憧憬</h2>
            <div class="future-content">
                <p class="romantic-text">
                    愿我们的爱情如初见时般美好，如今日般深情，如未来般永恒。
                    愿我们能够一起走过人生的每一个春夏秋冬，
                    一起看遍世界的美好风景，一起创造更多美好的回忆。
                </p>
                <div class="text-divider">
                    <span class="heart-divider">💕</span>
                </div>
                <p class="romantic-text">
                    在一起的日子里，我们学会了包容，学会了理解，学会了爱。
                    未来的路还很长，但只要有你在身边，我就有勇气面对一切。
                    让我们继续书写属于我们的爱情故事，直到永远。
                </p>
            </div>
        </section>
    </div>

    <!-- 引入前端配置 -->
    <script src="/src/client/scripts/config.js"></script>

    <!-- 智能视频加载器 - 四层CDN架构 -->
    <script src="/src/client/scripts/video-loader.js"></script>

    <!-- 引入现代爱情话语数据 -->
    <script src="/src/client/scripts/modern-quotes-data.js"></script>

    <script>
        // API 基础路径 - 从配置获取
        let API_BASE;

        // 数据存储变量
        let memoriesData = [];
        let timelineData = [];

        // API 调用函数
        async function apiCall(url, options = {}) {
            try {
                const response = await fetch(url, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });
                const data = await response.json();
                return data;
            } catch (error) {
                console.error('API调用失败:', error);
                showNotification('网络错误，请稍后重试', 'error');
                return { success: false, message: error.message };
            }
        }

        // 获取美好瞬间数据
        async function loadMemoriesData() {
            const result = await apiCall(`${API_BASE}/memories`);
            if (result.success) {
                memoriesData = result.data;
                renderMemories();
            } else {
                showNotification('加载美好瞬间失败: ' + result.message, 'error');
            }
        }

        // 获取时光轴数据
        async function loadTimelineData() {
            const result = await apiCall(`${API_BASE}/timeline`);
            if (result.success) {
                timelineData = result.data;
                renderTimeline();
            } else {
                showNotification('加载时光轴失败: ' + result.message, 'error');
            }
        }

        // 时光轴管理功能 - 数据已在上面初始化为空数组

        // 全局函数 - 添加时刻弹窗
        window.openAddTimelineModal = function() {
            const modal = createModal('add', {});
            document.body.appendChild(modal);
        }

        // 全局函数 - 编辑时刻
        window.editTimeline = function(id) {
            const item = timelineData.find(t => t.id == id);
            if (item) {
                const modal = createModal('edit', item);
                document.body.appendChild(modal);
            }
        }

        // 全局函数 - 删除时刻
        window.deleteTimeline = async function(id) {
            if (confirm('确定要删除这个珍贵的回忆吗？💔')) {
                const result = await apiCall(`${API_BASE}/timeline/${id}`, {
                    method: 'DELETE'
                });

                if (result.success) {
                    // 从本地数据中移除
                    timelineData = timelineData.filter(t => t.id != id);
                    renderTimeline();
                    showNotification('时刻已删除', 'success');
                } else {
                    showNotification('删除失败: ' + result.message, 'error');
                }
            }
        }

        // === 美好瞬间管理功能 ===
        
        // 全局函数 - 添加美好瞬间弹窗
        window.openAddMemoryModal = function() {
            const modal = createMemoryModal('add', {});
            document.body.appendChild(modal);
        }

        // 全局函数 - 编辑美好瞬间
        window.editMemory = function(id) {
            const item = memoriesData.find(m => m.id == id);
            if (item) {
                const modal = createMemoryModal('edit', item);
                document.body.appendChild(modal);
            }
        }

        // 全局函数 - 删除美好瞬间
        window.deleteMemory = async function(id) {
            if (confirm('确定要删除这个美好瞬间吗？💔')) {
                const result = await apiCall(`${API_BASE}/memories/${id}`, {
                    method: 'DELETE'
                });

                if (result.success) {
                    // 从本地数据中移除
                    memoriesData = memoriesData.filter(m => m.id != id);
                    renderMemories();
                    showNotification('瞬间已删除', 'success');
                } else {
                    showNotification('删除失败: ' + result.message, 'error');
                }
            }
        }

        // 创建美好瞬间弹窗
        function createMemoryModal(type, data) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${type === 'add' ? '添加美好瞬间' : '编辑美好瞬间'}</h3>
                        <button class="modal-close" onclick="closeModal(this)">×</button>
                    </div>
                    <form class="modal-form" onsubmit="submitMemory(event, '${type}', ${data.id || 'null'})">
                        <div class="form-group">
                            <label>图标 (Font Awesome类名)</label>
                            <input type="text" name="icon" value="${data.icon || ''}" placeholder="例：fas fa-heart" required>
                            <small style="color: #666; font-size: 0.9rem;">可选择：fas fa-coffee, fas fa-music, fas fa-book, fas fa-heart, fas fa-camera 等</small>
                        </div>
                        <div class="form-group">
                            <label>标题</label>
                            <input type="text" name="title" value="${data.title || ''}" placeholder="例：美好的回忆" required>
                        </div>
                        <div class="form-group">
                            <label>描述</label>
                            <textarea name="content" placeholder="描述这个美好的瞬间..." required>${data.content || ''}</textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" onclick="closeModal(this)" class="btn-cancel">取消</button>
                            <button type="submit" class="btn-submit">${type === 'add' ? '添加瞬间' : '保存修改'}</button>
                        </div>
                    </form>
                </div>
            `;
            return modal;
        }

        // 全局函数 - 提交美好瞬间数据
        window.submitMemory = async function(event, type, id) {
            event.preventDefault();
            const form = event.target;
            const formData = new FormData(form);

            const memoryItem = {
                icon: formData.get('icon'),
                title: formData.get('title'),
                content: formData.get('content'),
                sort_order: memoriesData.length + 1
            };

            let result;
            if (type === 'add') {
                result = await apiCall(`${API_BASE}/memories`, {
                    method: 'POST',
                    body: JSON.stringify(memoryItem)
                });

                if (result.success) {
                    memoriesData.push(result.data);
                    showNotification('美好瞬间已添加', 'success');
                }
            } else {
                result = await apiCall(`${API_BASE}/memories/${id}`, {
                    method: 'PUT',
                    body: JSON.stringify(memoryItem)
                });

                if (result.success) {
                    const index = memoriesData.findIndex(m => m.id == id);
                    if (index !== -1) {
                        memoriesData[index] = { ...memoriesData[index], ...memoryItem };
                    }
                    showNotification('美好瞬间已更新', 'success');
                }
            }

            if (result.success) {
                renderMemories();
                closeModal(form.querySelector('.modal-close'));
            } else {
                showNotification('操作失败: ' + result.message, 'error');
            }
        }

        // 渲染美好瞬间
        function renderMemories() {
            const memoryGrid = document.querySelector('.memory-grid');
            memoryGrid.innerHTML = memoriesData.map(item => `
                <div class="memory-card" data-id="${item.id}">
                    <div class="memory-actions">
                        <button class="action-btn edit-btn" onclick="window.editMemory(${item.id})" title="✏️ 编辑这个瞬间">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="window.deleteMemory(${item.id})" title="🗑️ 删除这个瞬间">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="memory-card-icon">
                        <i class="${item.icon}"></i>
                    </div>
                    <h3 class="memory-card-title">${item.title}</h3>
                    <p class="memory-card-content">${item.content}</p>
                </div>
            `).join('');
            
            // 添加入场动画
            setTimeout(() => {
                const cards = memoryGrid.querySelectorAll('.memory-card');
                cards.forEach((card, index) => {
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0) scale(1)';
                    }, index * 150);
                });
            }, 100);
        }

        // 创建弹窗
        function createModal(type, data) {
            const modal = document.createElement('div');
            modal.className = 'modal-overlay';
            modal.innerHTML = `
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${type === 'add' ? '添加珍贵时刻' : '编辑珍贵时刻'}</h3>
                        <button class="modal-close" onclick="closeModal(this)">×</button>
                    </div>
                    <form class="modal-form" onsubmit="submitTimeline(event, '${type}', ${data.id || 'null'})">
                        <div class="form-group">
                            <label>日期</label>
                            <div class="input-with-button-container">
                                <div class="date-display-input" onclick="openDatePicker()">
                                    <span id="selected-date">${data.date ? formatDateDisplay(data.date) : '选择日期'}</span>
                                </div>
                                <button type="button" class="embedded-date-btn" onclick="openDatePicker()">📅</button>
                                <input type="hidden" id="date-value" name="date" value="${data.date || ''}" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>标题</label>
                            <div class="input-with-button-container">
                                <input type="text" name="title" id="title-input" value="${data.title || ''}" placeholder="选择emoji或输入标题" required>
                                <div class="emoji-selector-container">
                                    <button type="button" class="embedded-emoji-btn" onclick="toggleEmojiDropdown()">😊</button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Emoji弹窗背景遮罩 -->
                        <div class="emoji-backdrop" id="emoji-backdrop" onclick="closeEmojiDropdown()"></div>
                        
                        <!-- Emoji选择弹窗 -->
                        <div class="emoji-dropdown" id="emoji-dropdown">
                            <div class="emoji-category">
                                <div class="emoji-category-title">❤️ 爱情表达</div>
                                <div class="emoji-grid">
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💕')">💕</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💖')">💖</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💗')">💗</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💝')">💝</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💘')">💘</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💞')">💞</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💓')">💓</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💟')">💟</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('❤️')">❤️</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🧡')">🧡</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💛')">💛</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💚')">💚</button>
                                </div>
                            </div>
                            <div class="emoji-category">
                                <div class="emoji-category-title">🎉 庆祝时刻</div>
                                <div class="emoji-grid">
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🎉')">🎉</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🎊')">🎊</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🎈')">🎈</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🎁')">🎁</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🎂')">🎂</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🍰')">🍰</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🥂')">🥂</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🍾')">🍾</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🎆')">🎆</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🎇')">🎇</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('✨')">✨</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🌟')">🌟</button>
                                </div>
                            </div>
                            <div class="emoji-category">
                                <div class="emoji-category-title">🌸 浪漫符号</div>
                                <div class="emoji-grid">
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🌹')">🌹</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🌺')">🌺</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🌸')">🌸</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🌷')">🌷</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🌻')">🌻</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🌙')">🌙</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('⭐')">⭐</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('☀️')">☀️</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🌈')">🌈</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🦋')">🦋</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💫')">💫</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🌠')">🌠</button>
                                </div>
                            </div>
                            <div class="emoji-category">
                                <div class="emoji-category-title">😊 表情符号</div>
                                <div class="emoji-grid">
                                    <button type="button" class="emoji-option" onclick="insertEmoji('😊')">😊</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('😍')">😍</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🥰')">🥰</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('😘')">😘</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💋')">💋</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('😇')">😇</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🤗')">🤗</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('😎')">😎</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('😻')">😻</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('🥳')">🥳</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('👑')">👑</button>
                                    <button type="button" class="emoji-option" onclick="insertEmoji('💎')">💎</button>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label>描述</label>
                            <textarea name="description" placeholder="记录这个美好时刻的详细描述..." required>${data.description || ''}</textarea>
                        </div>
                        <div class="form-actions">
                            <button type="button" onclick="closeModal(this)" class="btn-cancel">取消</button>
                            <button type="submit" class="btn-submit">${type === 'add' ? '添加时刻' : '保存修改'}</button>
                        </div>
                    </form>
                </div>
            `;
            return modal;
        }

        // 全局函数 - 关闭弹窗
        window.closeModal = function(btn) {
            const modal = btn.closest('.modal-overlay');
            modal.remove();
        }
        
        // 全局函数 - 切换emoji下拉菜单
        window.toggleEmojiDropdown = function() {
            const dropdown = document.getElementById('emoji-dropdown');
            const backdrop = document.getElementById('emoji-backdrop');
            
            if (dropdown && backdrop) {
                const isVisible = dropdown.classList.contains('show');
                if (isVisible) {
                    dropdown.classList.remove('show');
                    backdrop.classList.remove('show');
                } else {
                    dropdown.classList.add('show');
                    backdrop.classList.add('show');
                }
            }
        }
        
        // 全局函数 - 关闭emoji下拉菜单
        window.closeEmojiDropdown = function() {
            const dropdown = document.getElementById('emoji-dropdown');
            const backdrop = document.getElementById('emoji-backdrop');
            
            if (dropdown) {
                dropdown.classList.remove('show');
            }
            if (backdrop) {
                backdrop.classList.remove('show');
            }
        }
        
        // 全局函数 - 插入emoji到标题输入框
        window.insertEmoji = function(emoji) {
            const titleInput = document.getElementById('title-input');
            if (titleInput) {
                const currentValue = titleInput.value;
                const cursorPosition = titleInput.selectionStart;
                const newValue = currentValue.slice(0, cursorPosition) + emoji + ' ' + currentValue.slice(cursorPosition);
                titleInput.value = newValue;
                titleInput.focus();
                titleInput.setSelectionRange(cursorPosition + emoji.length + 1, cursorPosition + emoji.length + 1);
                
                // 选择emoji后自动关闭下拉菜单
                closeEmojiDropdown();
            }
        }
        
        // 全局变量 - 当前日期选择器状态
        let currentYear = new Date().getFullYear();
        let currentMonth = new Date().getMonth();
        
        // 初始化日期选择器状态
        function initDatePicker() {
            const today = new Date();
            currentYear = today.getFullYear();
            currentMonth = today.getMonth();
        }
        
        // 格式化日期显示 - 按照北京时间处理
        function formatDateDisplay(dateStr) {
            if (!dateStr) return '选择日期';
            // 明确按照北京时间处理，避免时区问题
            const parts = dateStr.split('-');
            const year = parseInt(parts[0]);
            const month = parseInt(parts[1]);
            const day = parseInt(parts[2]);
            return `${year}年${month.toString().padStart(2, '0')}月${day.toString().padStart(2, '0')}日`;
        }
        
        // 全局函数 - 打开自定义日期选择器
        window.openDatePicker = function() {
            // 确保日期选择器状态正确初始化
            initDatePicker();
            
            // 获取当前选中的日期，如果没有则使用今天
            const dateValueEl = document.getElementById('date-value');
            const selectedDateEl = document.getElementById('selected-date');
            const today = new Date();
            
            if (dateValueEl && dateValueEl.value) {
                const selectedDate = new Date(dateValueEl.value);
                if (!isNaN(selectedDate.getTime())) {
                    currentYear = selectedDate.getFullYear();
                    currentMonth = selectedDate.getMonth();
                }
            } else {
                // 没有选中日期时，默认显示今天所在的月份并选择今天
                currentYear = today.getFullYear();
                currentMonth = today.getMonth();
                // 使用本地时间生成今天的日期字符串
                const todayYear = today.getFullYear();
                const todayMonth = (today.getMonth() + 1).toString().padStart(2, '0');
                const todayDay = today.getDate().toString().padStart(2, '0');
                const todayStr = `${todayYear}-${todayMonth}-${todayDay}`;
                if (dateValueEl) {
                    dateValueEl.value = todayStr;
                }
                if (selectedDateEl) {
                    selectedDateEl.textContent = formatDateDisplay(todayStr);
                }
            }
            
            const overlay = document.createElement('div');
            overlay.className = 'date-picker-overlay';
            overlay.onclick = function(e) {
                if (e.target === this) {
                    closeDatePicker();
                }
            };
            
            const modal = document.createElement('div');
            modal.className = 'date-picker-modal';
            modal.innerHTML = `
                <div class="date-picker-header">
                    <button class="date-picker-nav" onclick="changeMonth(-1)">‹</button>
                    <h3>${currentYear}年 ${currentMonth + 1}月</h3>
                    <button class="date-picker-nav" onclick="changeMonth(1)">›</button>
                </div>
                <div class="date-picker-grid" id="date-grid">
                    <!-- 日期网格将由JavaScript生成 -->
                </div>
            `;
            
            overlay.appendChild(modal);
            document.body.appendChild(overlay);
            
            renderCalendar();
        }
        
        // 生成日历
        function renderCalendar() {
            const grid = document.getElementById('date-grid');
            if (!grid) {
                console.error('日期网格元素未找到');
                return;
            }
            
            // 确保currentYear和currentMonth是有效数值
            if (isNaN(currentYear) || isNaN(currentMonth)) {
                console.error('年份或月份无效:', currentYear, currentMonth);
                initDatePicker();
            }
            
            const firstDay = new Date(currentYear, currentMonth, 1);
            const startDate = new Date(firstDay);
            startDate.setDate(startDate.getDate() - firstDay.getDay());
            
            const today = new Date();
            // 使用本地时间生成今天的日期字符串
            const todayYear = today.getFullYear();
            const todayMonth = (today.getMonth() + 1).toString().padStart(2, '0');
            const todayDay = today.getDate().toString().padStart(2, '0');
            const todayStr = `${todayYear}-${todayMonth}-${todayDay}`;
            
            let html = '';
            const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
            
            // 添加星期标题
            weekdays.forEach(day => {
                html += `<div class="date-picker-day" style="font-weight: bold; cursor: default; border: none; box-shadow: none; color: #22d3ee;">${day}</div>`;
            });
            
            // 生成6周的日期
            for (let week = 0; week < 6; week++) {
                for (let day = 0; day < 7; day++) {
                    const date = new Date(startDate);
                    date.setDate(startDate.getDate() + week * 7 + day);
                    
                    const isCurrentMonth = date.getMonth() === currentMonth;
                    // 使用本地时间生成日期字符串，避免UTC转换
                    const year = date.getFullYear();
                    const month = (date.getMonth() + 1).toString().padStart(2, '0');
                    const dayNum = date.getDate().toString().padStart(2, '0');
                    const dateStr = `${year}-${month}-${dayNum}`;
                    
                    const dateValueEl = document.getElementById('date-value');
                    const isSelected = dateValueEl && dateValueEl.value === dateStr;
                    const isToday = dateStr === todayStr;
                    
                    // 按照北京时间检查是否为未来日期
                    const dateParts = dateStr.split('-');
                    const dateYear = parseInt(dateParts[0]);
                    const dateMonth = parseInt(dateParts[1]) - 1; // 月份从0开始
                    const dateDay = parseInt(dateParts[2]);
                    
                    const todayForComparison = new Date();
                    const todayYear = todayForComparison.getFullYear();
                    const todayMonth = todayForComparison.getMonth();
                    const todayDay = todayForComparison.getDate();
                    
                    const isFuture = dateYear > todayYear || 
                        (dateYear === todayYear && dateMonth > todayMonth) ||
                        (dateYear === todayYear && dateMonth === todayMonth && dateDay > todayDay);
                    
                    let classes = 'date-picker-day';
                    if (!isCurrentMonth) classes += ' other-month';
                    if (isToday) classes += ' today';
                    if (isSelected) classes += ' selected';
                    if (isFuture) classes += ' future';
                    
                    // 未来日期不可点击
                    const onclick = isFuture ? '' : `onclick="selectDate('${dateStr}')"`;
                    
                    html += `<div class="${classes}" ${onclick}>${date.getDate()}</div>`;
                }
            }
            
            grid.innerHTML = html;
        }
        
        // 选择日期
        window.selectDate = function(dateStr) {
            // 按照北京时间检查是否选择了未来日期
            const selectedParts = dateStr.split('-');
            const selectedYear = parseInt(selectedParts[0]);
            const selectedMonth = parseInt(selectedParts[1]) - 1; // 月份从0开始
            const selectedDay = parseInt(selectedParts[2]);
            
            const today = new Date();
            const todayYear = today.getFullYear();
            const todayMonth = today.getMonth();
            const todayDay = today.getDate();
            
            // 比较年月日
            if (selectedYear > todayYear || 
                (selectedYear === todayYear && selectedMonth > todayMonth) ||
                (selectedYear === todayYear && selectedMonth === todayMonth && selectedDay > todayDay)) {
                alert('不能选择未来的日期哦 💕');
                return;
            }
            
            const dateValue = document.getElementById('date-value');
            const selectedDateEl = document.getElementById('selected-date');
            
            if (dateValue && selectedDateEl) {
                dateValue.value = dateStr;
                // 直接使用格式化函数，确保一致的北京时间处理
                selectedDateEl.textContent = formatDateDisplay(dateStr);
            }
            
            closeDatePicker();
        }
        
        // 切换月份
        window.changeMonth = function(delta) {
            // 确保currentYear和currentMonth是有效数值
            if (isNaN(currentYear) || isNaN(currentMonth)) {
                initDatePicker();
            }
            
            currentMonth += delta;
            if (currentMonth < 0) {
                currentMonth = 11;
                currentYear--;
            } else if (currentMonth > 11) {
                currentMonth = 0;
                currentYear++;
            }
            
            // 验证计算后的年月是否有效
            if (isNaN(currentYear) || isNaN(currentMonth)) {
                console.error('月份切换后年月无效:', currentYear, currentMonth);
                initDatePicker();
            }
            
            const header = document.querySelector('.date-picker-header h3');
            if (header) {
                header.textContent = `${currentYear}年 ${currentMonth + 1}月`;
            }
            
            renderCalendar();
        }
        
        // 关闭日期选择器
        function closeDatePicker() {
            const overlay = document.querySelector('.date-picker-overlay');
            if (overlay) {
                overlay.remove();
            }
        }

        // 全局函数 - 提交时光轴数据
        window.submitTimeline = async function(event, type, id) {
            event.preventDefault();
            const form = event.target;
            const formData = new FormData(form);

            const timelineItem = {
                date: formData.get('date'),
                title: formData.get('title'),
                description: formData.get('description'),
                sort_order: timelineData.length + 1
            };

            let result;
            if (type === 'add') {
                result = await apiCall(`${API_BASE}/timeline`, {
                    method: 'POST',
                    body: JSON.stringify(timelineItem)
                });

                if (result.success) {
                    timelineData.push(result.data);
                    showNotification('珍贵时刻已添加', 'success');
                }
            } else {
                result = await apiCall(`${API_BASE}/timeline/${id}`, {
                    method: 'PUT',
                    body: JSON.stringify(timelineItem)
                });

                if (result.success) {
                    const index = timelineData.findIndex(t => t.id == id);
                    if (index !== -1) {
                        timelineData[index] = { ...timelineData[index], ...timelineItem };
                    }
                    showNotification('珍贵时刻已更新', 'success');
                }
            }

            if (result.success) {
                renderTimeline();
                // 修复关闭对话框的逻辑
                const modal = form.closest('.modal-overlay');
                if (modal) {
                    modal.remove();
                }
            } else {
                showNotification('操作失败: ' + result.message, 'error');
            }
        }

        // 渲染时光轴
        function renderTimeline() {
            const timeline = document.querySelector('.timeline');
            timeline.innerHTML = timelineData.map((item, index) => `
                <div class="timeline-item ${index % 2 === 0 ? 'left' : 'right'}" 
                     data-id="${item.id}" 
                     style="--item-index: ${index};">
                    <div class="timeline-content">
                        <div class="timeline-header">
                            <h3 class="timeline-title">${item.title}</h3>
                            <span class="timeline-separator">💕</span>
                            <div class="timeline-date">${item.date}</div>
                        </div>
                        <p class="timeline-description">${item.description}</p>
                        <div class="embedded-actions">
                            <button class="embedded-btn edit-btn" onclick="window.editTimeline(${item.id})" title="✏️ 编辑这个珍贵时刻">
                                <i class="fas fa-edit"></i> 编辑
                            </button>
                            <button class="embedded-btn delete-btn" onclick="window.deleteTimeline(${item.id})" title="🗑️ 删除这个时刻">
                                <i class="fas fa-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                </div>
            `).join('');
            
            // 添加入场动画
            setTimeout(() => {
                const items = timeline.querySelectorAll('.timeline-item');
                items.forEach((item, index) => {
                    setTimeout(() => {
                        item.style.opacity = '1';
                        item.style.transform = 'translateY(0)';
                    }, index * 200);
                });
            }, 100);
        }

        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification notification-${type}`;

            let icon = 'info-circle';
            if (type === 'success') icon = 'check-circle';
            else if (type === 'error') icon = 'exclamation-circle';

            notification.innerHTML = `
                <i class="fas fa-${icon}"></i>
                ${message}
            `;
            document.body.appendChild(notification);

            setTimeout(() => {
                notification.classList.add('show');
            }, 100);

            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => notification.remove(), 300);
            }, 3000);
        }

        // 海底视频背景加载处理
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('🌊 页面加载完成，初始化组件...');

            // 首先加载配置
            try {
                await loadConfig();
                API_BASE = window.LOVE_CONFIG.API_URL.replace(window.LOVE_CONFIG.BASE_URL, '');
                console.log('✅ 配置加载成功，API_BASE:', API_BASE);
            } catch (error) {
                console.error('❌ 配置加载失败:', error.message);
                API_BASE = '/api'; // 临时fallback
            }

            // 初始化日期选择器
            initDatePicker();

            // 视频加载状态标记
            let videoInitialized = false;
            let videoLoadingStarted = false;
            console.log('📅 日期选择器初始化完成');

            // 从API加载数据
            console.log('📡 开始从API加载数据...');
            await Promise.all([
                loadTimelineData(),
                loadMemoriesData()
            ]);
            console.log('✨ 时光轴渲染完成，共', timelineData.length, '个时刻');
            console.log('💫 美好瞬间渲染完成，共', memoriesData.length, '个瞬间');
            
            // 添加点击外部关闭emoji下拉菜单的功能
            document.addEventListener('click', function(event) {
                const emojiButton = event.target.closest('.embedded-emoji-btn');
                const emojiDropdown = event.target.closest('.emoji-dropdown');
                
                // 如果点击的不是emoji按钮也不是下拉菜单内部，则关闭菜单
                if (!emojiButton && !emojiDropdown) {
                    closeEmojiDropdown();
                }
            });
            console.log('🎨 Emoji选择器事件监听器已初始化');
            
            // 智能视频加载器集成 - 四层CDN架构
            const USE_SMART_LOADER = true; // 新旧系统切换开关

            const video = document.querySelector('.video-background video');
            const videoContainer = document.querySelector('.video-background');
            const loadingOverlay = document.getElementById('loadingOverlay');
            const loadingProgress = document.getElementById('loadingProgress');

            // 视频加载状态标记 (使用已声明的变量)

            // 隐藏加载遮罩 (使用和主页相同的系统)
            function hideLoadingOverlay() {
                console.log('🎭 开始隐藏加载遮罩:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
                const loadingOverlay = document.getElementById('loadingOverlay');
                const loadingProgress = document.getElementById('loadingProgress');

                if (loadingProgress) loadingProgress.style.width = '100%';

                setTimeout(function() {
                    if (loadingOverlay) {
                        loadingOverlay.classList.add('hidden');
                        console.log('🎭 加载遮罩已隐藏:', new Date().toISOString(), '相对时间:', performance.now().toFixed(2) + 'ms');
                    }
                }, 300);
            }

            if (USE_SMART_LOADER && typeof VideoLoader !== 'undefined') {
                // 使用四层智能加载器
                console.log('🎬 启用四层智能视频加载器 - 在一起的日子');

                VideoLoader.integrateWithPage({
                    pageName: 'together-days',
                    videoSelector: '.video-background video',
                    loadingOverlaySelector: '#loadingOverlay',
                    loadingProgressSelector: '#loadingProgress'
                });

            } else if (video) {
                // 降级到原有加载逻辑 (保持完整兼容性)
                console.log('📼 使用原有视频加载逻辑 - 在一起的日子');
                loadVideoWithOriginalMethod();
            } else {
                // 如果没有视频元素，直接隐藏加载遮罩
                hideLoadingOverlay();
            }

            // 原有加载逻辑封装 (作为降级方案)
            function loadVideoWithOriginalMethod() {
                let progressInterval;
                let currentProgress = 0;

                // 模拟加载进度
                function updateProgress() {
                    if (currentProgress < 90) {
                        currentProgress += Math.random() * 15;
                        if (currentProgress > 90) currentProgress = 90;
                        if (loadingProgress) loadingProgress.style.width = currentProgress + '%';
                    }
                }

                // 开始进度模拟
                progressInterval = setInterval(updateProgress, 200);

                // 视频开始加载
                video.addEventListener('loadstart', function() {
                    if (!videoLoadingStarted) {
                        console.log('海底视频开始加载...');
                        videoLoadingStarted = true;
                    }
                });

                // 视频有足够数据可以播放
                video.addEventListener('canplay', function() {
                    if (!videoInitialized) {
                        console.log('海底视频可以播放');
                        clearInterval(progressInterval);
                        currentProgress = 95;
                        if (loadingProgress) loadingProgress.style.width = currentProgress + '%';

                        // 确保视频自动播放
                        video.play().catch(function(error) {
                            console.log('视频自动播放被阻止:', error);
                            hideLoadingOverlay();
                        });
                    }
                });

                // 视频加载完成并开始播放
                video.addEventListener('playing', function() {
                    if (!videoInitialized) {
                        console.log('海底视频背景加载成功并开始播放');
                        clearInterval(progressInterval);
                        video.classList.add('loaded');
                        if (videoContainer) videoContainer.classList.add('video-loaded');
                        hideLoadingOverlay();
                        videoInitialized = true;
                    }
                });

                // 视频加载失败
                video.addEventListener('error', function() {
                    if (!videoInitialized) {
                        console.log('海底视频背景加载失败，使用海洋主题备用背景');
                        clearInterval(progressInterval);
                        video.style.display = 'none';
                        if (videoContainer) {
                            videoContainer.style.background = 'linear-gradient(135deg, #0c4a6e 0%, #075985 25%, #0369a1 50%, #0284c7 75%, #0ea5e9 100%)';
                            videoContainer.classList.add('video-loaded');
                        }
                        hideLoadingOverlay();
                        videoInitialized = true;
                    }
                });

                // 设置超时
                setTimeout(function() {
                    if (!video.classList.contains('loaded')) {
                        console.log('视频加载超时，切换到海洋主题背景');
                        clearInterval(progressInterval);
                        if (videoContainer) {
                            videoContainer.style.background = 'linear-gradient(135deg, #0c4a6e 0%, #075985 25%, #0369a1 50%, #0284c7 75%, #0ea5e9 100%)';
                            videoContainer.classList.add('video-loaded');
                        }
                        hideLoadingOverlay();
                    }
                }, 10000);
            }

            // 初始化爱情感悟功能
            console.log('💕 初始化爱情感悟功能...');
            // 延迟一点时间，确保modern-quotes-data.js加载完成
            setTimeout(() => {
                if (typeof refreshQuote === 'function') {
                    refreshQuote();
                    console.log('💕 爱情感悟初始化完成');
                } else {
                    console.warn('💕 refreshQuote函数未定义，爱情感悟功能可能未正确加载');
                }
            }, 800); // 稍微增加延迟时间
        });

        // === 爱情感悟随机换句功能 ===

        // 当前显示的话语索引
        let currentQuoteIndex = -1;

        // 获取随机话语
        function getRandomQuote() {
            // 确保 modernLoveQuotes 数据已加载
            if (typeof modernLoveQuotes === 'undefined') {
                console.warn('💔 现代爱情话语数据未加载：modernLoveQuotes 未定义');
                console.log('🔍 检查script标签是否正确加载：/love/modern-quotes-data.js');
                return {
                    content: "爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏一个不完美的人。在一起的每一天，都让我更加确信，你就是我要找的那个人。",
                    source: "我们的爱情感悟"
                };
            }

            if (!modernLoveQuotes || modernLoveQuotes.length === 0) {
                console.warn('💔 现代爱情话语数据为空或无效');
                console.log('🔍 modernLoveQuotes类型:', typeof modernLoveQuotes);
                console.log('🔍 modernLoveQuotes长度:', modernLoveQuotes ? modernLoveQuotes.length : 'N/A');
                return {
                    content: "爱情不是寻找一个完美的人，而是学会用完美的眼光欣赏一个不完美的人。在一起的每一天，都让我更加确信，你就是我要找的那个人。",
                    source: "我们的爱情感悟"
                };
            }

            // 首次成功加载时输出日志
            if (currentQuoteIndex === -1) {
                console.log('💕 现代爱情话语数据加载成功！总数:', modernLoveQuotes.length);
            }

            let newIndex;
            // 确保不重复显示同一句话
            do {
                newIndex = Math.floor(Math.random() * modernLoveQuotes.length);
            } while (newIndex === currentQuoteIndex && modernLoveQuotes.length > 1);

            currentQuoteIndex = newIndex;
            return modernLoveQuotes[newIndex];
        }

        // 刷新话语的动画效果
        function refreshQuote() {
            const quoteText = document.getElementById('currentQuote');
            const quoteAuthor = document.getElementById('currentQuoteAuthor');
            const refreshBtn = document.querySelector('.quote-refresh-btn');

            if (!quoteText || !quoteAuthor) {
                console.error('💔 找不到话语显示元素');
                console.log('🔍 检查元素ID: currentQuote, currentQuoteAuthor');
                return;
            }

            // 禁用按钮，防止快速点击
            if (refreshBtn) {
                refreshBtn.disabled = true;
                refreshBtn.style.opacity = '0.6';
            }

            // 添加淡出动画
            quoteText.classList.add('changing');
            quoteAuthor.classList.add('changing');

            // 获取新的话语
            const newQuote = getRandomQuote();

            // 延迟更新内容，等待淡出动画完成
            setTimeout(() => {
                // 更新内容
                quoteText.innerHTML = `"${newQuote.content}"`;
                quoteAuthor.innerHTML = `— ${newQuote.source || '现代情话'}`;

                // 移除动画类，触发淡入效果
                quoteText.classList.remove('changing');
                quoteAuthor.classList.remove('changing');

                // 重新启用按钮
                setTimeout(() => {
                    if (refreshBtn) {
                        refreshBtn.disabled = false;
                        refreshBtn.style.opacity = '1';
                    }
                }, 200);

            }, 250); // 等待淡出动画完成
        }

        // 页面加载时随机显示一句话语（已整合到主DOMContentLoaded事件中）

        // 全局函数，供按钮调用
        window.refreshQuote = refreshQuote;

        // 页面卸载时清理视频资源
        window.addEventListener('beforeunload', function() {
            const video = document.querySelector('.video-background video');
            if (video) {
                video.pause();
                video.src = '';
                video.load();
                console.log('🧹 页面卸载，已清理视频资源');
            }
        });

        // 页面隐藏时暂停视频，显示时恢复播放
        document.addEventListener('visibilitychange', function() {
            const video = document.querySelector('.video-background video');
            if (video && videoInitialized) {
                if (document.hidden) {
                    video.pause();
                    console.log('📱 页面隐藏，暂停视频播放');
                } else {
                    video.play().catch(function(error) {
                        console.log('📱 页面显示，恢复视频播放失败:', error);
                    });
                    console.log('📱 页面显示，恢复视频播放');
                }
            }
        });

    </script>
</body>
</html>
