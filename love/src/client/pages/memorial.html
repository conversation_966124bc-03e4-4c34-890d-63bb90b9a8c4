<!DOCTYPE html>
<html lang="zh-CN" style="background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%) !important; margin: 0; padding: 0;">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="珍藏我们爱情路上的每一件纪念物和礼物">
    <title>纪念物 - Yu 💕 Wang</title>
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>💕</text></svg>">

    <!-- 立即应用背景样式，防止白色闪烁 -->
    <style>
        html {
            background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%) !important;
            margin: 0 !important;
            padding: 0 !important;
            min-height: 100vh !important;
        }
        body {
            background: transparent !important;
            margin: 0 !important;
            padding: 0 !important;
            min-height: 100vh !important;
        }
    </style>

    <!-- 立即创建加载遮罩 - 确保F5刷新时立即显示 -->
    <script>
        document.write(`
            <div id="immediateLoadingOverlay" style="
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                width: 100% !important;
                height: 100% !important;
                background: linear-gradient(135deg, #0c0c2e 0%, #1a1a3e 25%, #2d1b69 50%, #4a148c 75%, #6a1b9a 100%) !important;
                display: flex !important;
                flex-direction: column !important;
                justify-content: center !important;
                align-items: center !important;
                z-index: 99999 !important;
                opacity: 1 !important;
                visibility: visible !important;
            ">
                <div style="
                    width: 60px !important;
                    height: 60px !important;
                    border: 4px solid rgba(255, 255, 255, 0.3) !important;
                    border-top: 4px solid white !important;
                    border-radius: 50% !important;
                    animation: immediateSpinAnimation 1s linear infinite !important;
                    margin-bottom: 20px !important;
                "></div>
                <div style="
                    color: white !important;
                    font-size: 2.5rem !important;
                    font-weight: 700 !important;
                    text-align: center !important;
                    font-family: 'Dancing Script', 'Great Vibes', 'Courgette', cursive !important;
                    text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3) !important;
                    letter-spacing: 2px !important;
                ">🎁 Loading Memorial...</div>
            </div>
            <style>
                @keyframes immediateSpinAnimation {
                    0% { transform: rotate(0deg); }
                    100% { transform: rotate(360deg); }
                }
                #immediateLoadingOverlay.hidden {
                    opacity: 0 !important;
                    visibility: hidden !important;
                    transition: opacity 0.8s ease-out, visibility 0.8s ease-out !important;
                }
            </style>
        `);
        console.log('🚀 Memorial立即遮罩已创建');
    </script>

    <link rel="stylesheet" href="/src/client/styles/pages.css">
    <link href="https://fonts.googleapis.com/css2?family=Dancing+Script:wght@400;700&family=Poppins:wght@300;400;500;600;700&family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        /* 海洋视频背景样式 */
        .video-background {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            overflow: hidden;
        }

        .video-background video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            opacity: 0; /* 初始透明，加载完成后显示 */
            filter: contrast(1.1) saturate(1.2); /* 保持原始亮度，仅增强对比度和饱和度 */
            transition: opacity 1s ease-in-out; /* 平滑过渡效果 */
        }

        /* 视频加载完成后显示 */
        .video-background video.loaded {
            opacity: 1.0; /* 完全不透明，显示视频原始亮度 */
        }

        /* 视频加载时的备用背景 */
        .video-background::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                #0c0c2e 0%,
                #1a1a3e 25%,
                #2d1b69 50%,
                #4a148c 75%,
                #6a1b9a 100%);
            z-index: -1;
            transition: opacity 1s ease-in-out;
        }

        /* 视频加载完成后隐藏备用背景 */
        .video-background.video-loaded::before {
            opacity: 0;
        }

        /* 加载遮罩样式 */
        .loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg,
                #0ea5e9 0%,
                #0284c7 25%,
                #0369a1 50%,
                #075985 75%,
                #0c4a6e 100%);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
            transition: opacity 0.8s ease-out, visibility 0.8s ease-out;
        }

        .loading-overlay.hidden {
            opacity: 0;
            visibility: hidden;
        }

        /* 加载动画 */
        .loading-spinner {
            width: 60px;
            height: 60px;
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            color: white;
            font-family: 'Dancing Script', cursive;
            font-size: 1.5rem;
            font-weight: 600;
            text-align: center;
            margin-bottom: 10px;
        }

        .loading-subtitle {
            color: rgba(255, 255, 255, 0.8);
            font-family: 'Inter', sans-serif;
            font-size: 1rem;
            text-align: center;
        }

        /* 加载进度条 */
        .loading-progress {
            width: 200px;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            margin-top: 20px;
            overflow: hidden;
        }

        .loading-progress-bar {
            height: 100%;
            background: white;
            border-radius: 2px;
            width: 0%;
            transition: width 0.3s ease;
            animation: progressPulse 2s ease-in-out infinite;
        }

        @keyframes progressPulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.7; }
        }

        /* 覆盖原有的body背景，让视频背景显示 */
        body {
            background: transparent !important; /* 移除遮罩，让视频背景完全显示 */
        }

        /* 调整星空效果的透明度，与海洋背景更好融合 */
        body::before {
            opacity: 0.2;
        }

        body::after {
            opacity: 0.15;
        }

        /* 增强内容区域的可读性 */
        .content-section {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .page-header {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
        }

        .memorial-item {
            /* 移除磨砂效果，让背景完全透明 */
            background: transparent !important;
        }

        .memorial-gallery {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 30px;
            margin-top: 30px;
        }
        
        .memorial-item {
            border-radius: 20px;
            padding: 30px;
            /* 移除磨砂效果，让背景完全透明 */
            border: 1px solid rgba(255, 255, 255, 0.3);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
            animation: cardGlow 5s ease-in-out infinite;
        }

        /* 粉色系渐变变化 - 基础粉色 */
        .memorial-item:nth-child(1) {
            background: linear-gradient(135deg,
                rgba(255, 182, 193, 0.9) 0%,
                rgba(255, 192, 203, 0.8) 25%,
                rgba(255, 218, 185, 0.9) 50%,
                rgba(255, 228, 225, 0.8) 75%,
                rgba(255, 240, 245, 0.9) 100%);
            box-shadow: 0 20px 40px rgba(255, 105, 180, 0.3),
                        0 0 30px rgba(255, 182, 193, 0.2);
        }

        /* 玫瑰粉色 */
        .memorial-item:nth-child(2) {
            background: linear-gradient(135deg,
                rgba(255, 20, 147, 0.15) 0%,
                rgba(255, 105, 180, 0.2) 25%,
                rgba(255, 182, 193, 0.25) 50%,
                rgba(255, 192, 203, 0.2) 75%,
                rgba(255, 228, 225, 0.15) 100%);
            box-shadow: 0 20px 40px rgba(255, 20, 147, 0.2),
                        0 0 30px rgba(255, 105, 180, 0.15);
        }

        /* 珊瑚粉色 */
        .memorial-item:nth-child(3) {
            background: linear-gradient(135deg,
                rgba(255, 127, 80, 0.15) 0%,
                rgba(255, 160, 122, 0.2) 25%,
                rgba(255, 182, 193, 0.25) 50%,
                rgba(255, 218, 185, 0.2) 75%,
                rgba(255, 239, 213, 0.15) 100%);
            box-shadow: 0 20px 40px rgba(255, 127, 80, 0.2),
                        0 0 30px rgba(255, 160, 122, 0.15);
        }

        /* 樱花粉色 */
        .memorial-item:nth-child(4) {
            background: linear-gradient(135deg,
                rgba(255, 182, 193, 0.2) 0%,
                rgba(255, 192, 203, 0.25) 25%,
                rgba(255, 218, 185, 0.2) 50%,
                rgba(255, 228, 181, 0.25) 75%,
                rgba(255, 245, 238, 0.2) 100%);
            box-shadow: 0 20px 40px rgba(255, 182, 193, 0.25),
                        0 0 30px rgba(255, 192, 203, 0.2);
        }

        /* 薰衣草粉色 */
        .memorial-item:nth-child(5) {
            background: linear-gradient(135deg,
                rgba(230, 230, 250, 0.2) 0%,
                rgba(221, 160, 221, 0.25) 25%,
                rgba(255, 182, 193, 0.2) 50%,
                rgba(255, 192, 203, 0.25) 75%,
                rgba(255, 228, 225, 0.2) 100%);
            box-shadow: 0 20px 40px rgba(221, 160, 221, 0.2),
                        0 0 30px rgba(230, 230, 250, 0.15);
        }

        /* 蜜桃粉色 */
        .memorial-item:nth-child(6) {
            background: linear-gradient(135deg,
                rgba(255, 218, 185, 0.2) 0%,
                rgba(255, 228, 196, 0.25) 25%,
                rgba(255, 239, 213, 0.2) 50%,
                rgba(255, 245, 238, 0.25) 75%,
                rgba(255, 250, 240, 0.2) 100%);
            box-shadow: 0 20px 40px rgba(255, 218, 185, 0.25),
                        0 0 30px rgba(255, 228, 196, 0.2);
        }

        .memorial-item::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .memorial-item:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }
        
        .memorial-icon {
            font-size: 3.5rem;
            text-align: center;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .memorial-title {
            font-family: 'Playfair Display', serif;
            font-size: 1.6rem;
            font-weight: 600;
            color: #333;
            text-align: center;
            margin-bottom: 15px;
        }
        
        .memorial-date {
            font-family: 'Dancing Script', cursive;
            font-size: 1.2rem;
            color: #667eea;
            text-align: center;
            margin-bottom: 20px;
            font-weight: 600;
        }
        
        .memorial-description {
            color: #666;
            line-height: 1.7;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .memorial-significance {
            background: transparent; /* 完全透明，让花朵背景完全显示 */
            padding: 15px;
            border-radius: 10px;
            font-style: italic;
            color: #555;
        }
        
        .special-memorial {
            background: transparent; /* 完全透明，让花朵背景完全显示 */
            border: 2px solid rgba(240, 147, 251, 0.2);
        }
        
        .special-memorial::before {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            height: 8px;
        }
        
        .treasure-box {
            background: transparent; /* 完全透明，让花朵背景完全显示 */
            border: 2px solid rgba(255, 215, 0, 0.3);
            position: relative;
        }
        
        .treasure-box::before {
            background: linear-gradient(135deg, #ffd700 0%, #ffc107 100%);
            height: 6px;
        }
        
        .treasure-box::after {
            content: '✨';
            position: absolute;
            top: 15px;
            right: 15px;
            font-size: 1.5rem;
            animation: sparkle 2s ease-in-out infinite;
        }
        
        @keyframes sparkle {
            0%, 100% { opacity: 0.5; transform: scale(1); }
            50% { opacity: 1; transform: scale(1.2); }
        }
        
        .memory-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .stat-card {
            background: linear-gradient(135deg,
                rgba(255, 182, 193, 0.9) 0%,
                rgba(255, 192, 203, 0.8) 25%,
                rgba(255, 218, 185, 0.9) 50%,
                rgba(255, 228, 225, 0.8) 75%,
                rgba(255, 240, 245, 0.9) 100%);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 15px 35px rgba(255, 105, 180, 0.3),
                        0 0 25px rgba(255, 182, 193, 0.2);
            /* 移除磨砂效果，让背景完全透明 */
            border: 1px solid rgba(255, 255, 255, 0.3);
            position: relative;
            overflow: hidden;
            animation: cardGlow 4s ease-in-out infinite;
        }


        
        .stat-number {
            font-family: 'Playfair Display', serif;
            font-size: 2.5rem;
            font-weight: 700;
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            display: block;
        }
        
        .stat-label {
            color: #666;
            font-weight: 500;
            margin-top: 5px;
        }

        /* 管理功能样式 */
        .section-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 30px;
        }

        .add-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-family: 'Dancing Script', cursive;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }

        .memorial-actions {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            gap: 8px;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .memorial-item:hover .memorial-actions {
            opacity: 1;
        }

        .action-btn {
            width: 35px;
            height: 35px;
            border: none;
            border-radius: 50%;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            /* 移除磨砂效果，让背景完全透明 */
        }

        .edit-btn {
            background: rgba(52, 152, 219, 0.9);
            color: white;
        }

        .edit-btn:hover {
            background: rgba(52, 152, 219, 1);
            transform: scale(1.1);
        }

        .delete-btn {
            background: rgba(231, 76, 60, 0.9);
            color: white;
        }

        .delete-btn:hover {
            background: rgba(231, 76, 60, 1);
            transform: scale(1.1);
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background: linear-gradient(135deg,
                rgba(255, 182, 193, 0.95) 0%,
                rgba(255, 192, 203, 0.95) 25%,
                rgba(255, 218, 185, 0.95) 50%,
                rgba(255, 228, 225, 0.95) 75%,
                rgba(255, 240, 245, 0.95) 100%);
            border-radius: 20px;
            padding: 0;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(15px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .modal-header {
            padding: 25px 30px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.3);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .modal-header h3 {
            font-family: 'Playfair Display', serif;
            font-size: 1.5rem;
            color: #333;
            margin: 0;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #666;
            cursor: pointer;
            padding: 5px;
            border-radius: 50%;
            transition: all 0.3s ease;
        }

        .close-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            color: #333;
        }

        .modal-form {
            padding: 20px 30px 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #333;
            font-family: 'Dancing Script', cursive;
            font-size: 1.1rem;
        }

        .form-group input,
        .form-group select,
        .form-group textarea {
            width: 100%;
            padding: 12px 15px;
            border: 1px solid rgba(255, 255, 255, 0.5);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.8);
            font-family: 'Inter', sans-serif;
            font-size: 0.95rem;
            transition: all 0.3s ease;
        }

        .form-group input:focus,
        .form-group select:focus,
        .form-group textarea:focus {
            outline: none;
            border-color: #667eea;
            background: rgba(255, 255, 255, 0.95);
            box-shadow: 0 0 10px rgba(102, 126, 234, 0.2);
        }

        .form-group textarea {
            resize: vertical;
            min-height: 80px;
        }

        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }

        .cancel-btn,
        .save-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 25px;
            font-family: 'Dancing Script', cursive;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .cancel-btn {
            background: rgba(108, 117, 125, 0.8);
            color: white;
        }

        .cancel-btn:hover {
            background: rgba(108, 117, 125, 1);
            transform: translateY(-2px);
        }

        .save-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        }

        .save-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <!-- 加载遮罩 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <div class="loading-text">🌊 加载海洋背景中...</div>
        <div class="loading-subtitle">正在为您准备珍贵的纪念物回忆</div>
        <div class="loading-progress">
            <div class="loading-progress-bar" id="loadingProgress"></div>
        </div>
    </div>

    <!-- 海洋视频背景 -->
    <div class="video-background">
        <video autoplay muted loop playsinline preload="metadata">
            <source src="/src/client/assets/video-compressed/memorial.mp4" type="video/mp4">
            <!-- 如果浏览器不支持视频，显示备用背景 -->
            您的浏览器不支持视频播放。
        </video>
    </div>

    <!-- 流星效果 -->
    <div class="shooting-stars">
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
        <div class="shooting-star"></div>
    </div>

    <!-- 浪漫漂浮元素 -->
    <div class="hearts-container">
        <div class="heart type-1 small"></div>
        <div class="heart type-2 medium"></div>
        <div class="heart type-3 large"></div>
        <div class="heart type-4 small"></div>
        <div class="heart type-5 medium"></div>
        <div class="heart type-6 large"></div>
        <div class="heart type-7 small"></div>
        <div class="heart type-8 medium"></div>
        <div class="heart type-9 large"></div>
        <div class="heart type-10 small"></div>
        <div class="heart type-11 medium"></div>
        <div class="heart type-12 large"></div>
        <div class="heart type-1 medium"></div>
        <div class="heart type-3 small"></div>
        <div class="heart type-5 large"></div>
    </div>

    <div class="container">
        <!-- 返回按钮 -->
        <a href="javascript:void(0)" onclick="window.location.href = getHomeUrl()" class="back-button">
            <i class="fas fa-arrow-left"></i>
            返回主页
        </a>

        <!-- 页面头部 -->
        <header class="page-header">
            <i class="fas fa-gift page-icon"></i>
            <h1 class="page-title">纪念物</h1>
            <p class="page-subtitle">珍藏我们爱情路上的每一件珍贵物品</p>
        </header>

        <!-- 纪念物统计 -->
        <section class="content-section">
            <h2 class="section-title">我们的爱情珍藏</h2>
            <div class="memory-stats">
                <div class="stat-card">
                    <span class="stat-number">12</span>
                    <div class="stat-label">珍贵礼物</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">8</span>
                    <div class="stat-label">纪念物品</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">25</span>
                    <div class="stat-label">美好回忆</div>
                </div>
                <div class="stat-card">
                    <span class="stat-number">∞</span>
                    <div class="stat-label">爱的见证</div>
                </div>
            </div>
        </section>

        <!-- 珍贵纪念物 -->
        <section class="content-section">
            <div class="section-header">
                <h2 class="section-title">我们的珍贵纪念物</h2>
                <button class="add-btn" onclick="openAddModal()">
                    <i class="fas fa-plus"></i>
                    添加纪念物
                </button>
            </div>
            <div class="memorial-gallery">
                
                <div class="memorial-item special-memorial" data-id="1">
                    <div class="memorial-actions">
                        <button class="action-btn edit-btn" onclick="editMemorial(1)" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="deleteMemorial(1)" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="memorial-icon">
                        <i class="fas fa-gem"></i>
                    </div>
                    <h3 class="memorial-title">第一条项链</h3>
                    <div class="memorial-date">2023年5月14日</div>
                    <p class="memorial-description">
                        你送给我的第一份礼物，一条精美的银质项链，上面有一个小小的心形吊坠。
                        虽然不是很昂贵，但那份心意比什么都珍贵。
                    </p>
                    <div class="memorial-significance">
                        "这是我们爱情的第一个见证，也是我最珍爱的饰品。每当戴上它，就能感受到你的爱意。"
                    </div>
                </div>

                <div class="memorial-item treasure-box" data-id="2">
                    <div class="memorial-actions">
                        <button class="action-btn edit-btn" onclick="editMemorial(2)" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="deleteMemorial(2)" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="memorial-icon">
                        <i class="fas fa-key"></i>
                    </div>
                    <h3 class="memorial-title">爱情钥匙</h3>
                    <div class="memorial-date">2023年6月20日</div>
                    <p class="memorial-description">
                        一把精致的小钥匙，你说这是你心房的钥匙，只有我才能打开。
                        这把钥匙现在被我小心翼翼地保存着。
                    </p>
                    <div class="memorial-significance">
                        "你给我的不只是一把钥匙，更是一份承诺，一个打开彼此心扉的约定。"
                    </div>
                </div>

                <div class="memorial-item" data-id="3">
                    <div class="memorial-actions">
                        <button class="action-btn edit-btn" onclick="editMemorial(3)" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="deleteMemorial(3)" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="memorial-icon">
                        <i class="fas fa-rose"></i>
                    </div>
                    <h3 class="memorial-title">第一朵玫瑰</h3>
                    <div class="memorial-date">2023年7月7日</div>
                    <p class="memorial-description">
                        七夕节那天，你送给我的第一朵红玫瑰。虽然花已经枯萎，
                        但我把它制成了干花，永远保存着那份美好。
                    </p>
                    <div class="memorial-significance">
                        "玫瑰会凋谢，但我们的爱情会永远绽放。这朵干花见证了我们的浪漫时光。"
                    </div>
                </div>

                <div class="memorial-item" data-id="4">
                    <div class="memorial-actions">
                        <button class="action-btn edit-btn" onclick="editMemorial(4)" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="deleteMemorial(4)" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="memorial-icon">
                        <i class="fas fa-camera"></i>
                    </div>
                    <h3 class="memorial-title">拍立得相机</h3>
                    <div class="memorial-date">2023年8月15日</div>
                    <p class="memorial-description">
                        你送给我的拍立得相机，说要记录我们在一起的每一个美好瞬间。
                        现在我们已经拍了好多张珍贵的照片。
                    </p>
                    <div class="memorial-significance">
                        "每一张照片都是我们爱情的片段，每一个瞬间都值得永远珍藏。"
                    </div>
                </div>

                <div class="memorial-item" data-id="5">
                    <div class="memorial-actions">
                        <button class="action-btn edit-btn" onclick="editMemorial(5)" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="deleteMemorial(5)" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="memorial-icon">
                        <i class="fas fa-book-heart"></i>
                    </div>
                    <h3 class="memorial-title">爱情日记本</h3>
                    <div class="memorial-date">2023年9月10日</div>
                    <p class="memorial-description">
                        一本精美的日记本，我们约定要在里面记录我们的爱情故事。
                        每一页都写满了我们的甜蜜回忆和对未来的憧憬。
                    </p>
                    <div class="memorial-significance">
                        "文字记录着我们的心情，日记见证着我们的成长，这是我们爱情最真实的记录。"
                    </div>
                </div>

                <div class="memorial-item">
                    <div class="memorial-icon">
                        <i class="fas fa-ring"></i>
                    </div>
                    <h3 class="memorial-title">情侣戒指</h3>
                    <div class="memorial-date">2023年10月23日</div>
                    <p class="memorial-description">
                        我们一起挑选的情侣戒指，简单却意义深远。戴在手上，
                        就像是对彼此的承诺，无论走到哪里都不会忘记。
                    </p>
                    <div class="memorial-significance">
                        "这枚戒指是我们爱情的象征，也是我们对未来的约定。"
                    </div>
                </div>

                <div class="memorial-item">
                    <div class="memorial-icon">
                        <i class="fas fa-teddy-bear"></i>
                    </div>
                    <h3 class="memorial-title">小熊玩偶</h3>
                    <div class="memorial-date">2023年11月11日</div>
                    <p class="memorial-description">
                        双十一那天你送给我的小熊玩偶，毛茸茸的很可爱。
                        你说当你不在身边的时候，让小熊陪伴我。
                    </p>
                    <div class="memorial-significance">
                        "这只小熊承载着你的温暖，每当想你的时候，抱着它就像抱着你一样。"
                    </div>
                </div>

                <div class="memorial-item">
                    <div class="memorial-icon">
                        <i class="fas fa-music"></i>
                    </div>
                    <h3 class="memorial-title">音乐盒</h3>
                    <div class="memorial-date">2023年12月25日</div>
                    <p class="memorial-description">
                        圣诞节的礼物，一个精美的音乐盒，里面播放着我们最喜欢的那首歌。
                        每当音乐响起，就想起我们一起度过的美好时光。
                    </p>
                    <div class="memorial-significance">
                        "音乐是我们爱情的背景，这个音乐盒奏响的是我们心中最美的旋律。"
                    </div>
                </div>

            </div>
        </section>

        <!-- 特殊纪念品 -->
        <section class="content-section">
            <h2 class="section-title">特殊的纪念品</h2>
            <div class="memory-grid">
                <div class="memory-card">
                    <div class="memory-card-icon">
                        <i class="fas fa-ticket-alt"></i>
                    </div>
                    <h3 class="memory-card-title">电影票收藏</h3>
                    <p class="memory-card-content">
                        我们一起看过的每一场电影的票根，都被我小心地保存着。
                        每一张票都代表着一次美好的约会。
                    </p>
                </div>

                <div class="memory-card">
                    <div class="memory-card-icon">
                        <i class="fas fa-leaf"></i>
                    </div>
                    <h3 class="memory-card-title">四叶草标本</h3>
                    <p class="memory-card-content">
                        那次散步时我们一起找到的四叶草，你说这代表着幸运。
                        现在它被制成了标本，永远保存着那份幸运。
                    </p>
                </div>

                <div class="memory-card">
                    <div class="memory-card-icon">
                        <i class="fas fa-envelope-heart"></i>
                    </div>
                    <h3 class="memory-card-title">手写情书</h3>
                    <p class="memory-card-content">
                        你亲手写给我的每一封情书，都被我珍藏在一个特殊的盒子里。
                        那些文字比任何礼物都珍贵。
                    </p>
                </div>

                <div class="memory-card">
                    <div class="memory-card-icon">
                        <i class="fas fa-star"></i>
                    </div>
                    <h3 class="memory-card-title">许愿瓶</h3>
                    <p class="memory-card-content">
                        我们一起制作的许愿瓶，里面装着我们对未来的美好愿望。
                        希望有一天这些愿望都能实现。
                    </p>
                </div>
            </div>
        </section>

        <!-- 爱情感悟 -->
        <section class="quote-section">
            <p class="quote-text">
                "每一件纪念物都承载着我们的回忆，每一份礼物都见证着我们的爱情。
                这些珍贵的物品，是我们爱情路上最美的风景。"
            </p>
            <p class="quote-author">— 我们的纪念物感悟</p>
        </section>

        <!-- 未来的纪念物 -->
        <section class="content-section">
            <h2 class="section-title">期待的未来纪念物</h2>
            <div class="section-content">
                <p>
                    还有很多美好的纪念物等待着我们去创造：订婚戒指、结婚戒指、
                    新房的钥匙、宝宝的第一件衣服...每一件都将成为我们爱情故事的新篇章。
                </p>
                <br>
                <p>
                    愿我们能够一直这样，用心收藏每一个美好的瞬间，
                    让这些纪念物见证我们从恋人到夫妻，从两个人到一家人的美好历程。
                </p>
            </div>
        </section>
    </div>

    <!-- 添加/编辑纪念物模态框 -->
    <div id="memorialModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 id="modalTitle">添加纪念物</h3>
                <button class="close-btn" onclick="closeModal()">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            <form id="memorialForm" class="modal-form">
                <input type="hidden" id="memorialId" value="">

                <div class="form-group">
                    <label for="memorialIcon">图标</label>
                    <select id="memorialIcon" required>
                        <option value="fas fa-gem">💎 宝石</option>
                        <option value="fas fa-key">🔑 钥匙</option>
                        <option value="fas fa-rose">🌹 玫瑰</option>
                        <option value="fas fa-camera">📷 相机</option>
                        <option value="fas fa-book-heart">📖 日记本</option>
                        <option value="fas fa-ring">💍 戒指</option>
                        <option value="fas fa-gift">🎁 礼物</option>
                        <option value="fas fa-heart">❤️ 爱心</option>
                        <option value="fas fa-star">⭐ 星星</option>
                        <option value="fas fa-envelope-heart">💌 情书</option>
                    </select>
                </div>

                <div class="form-group">
                    <label for="memorialTitle">标题</label>
                    <input type="text" id="memorialTitle" required placeholder="请输入纪念物名称">
                </div>

                <div class="form-group">
                    <label for="memorialDate">日期</label>
                    <input type="date" id="memorialDate" required>
                </div>

                <div class="form-group">
                    <label for="memorialDescription">描述</label>
                    <textarea id="memorialDescription" required placeholder="请描述这件纪念物的故事..."></textarea>
                </div>

                <div class="form-group">
                    <label for="memorialSignificance">意义</label>
                    <textarea id="memorialSignificance" required placeholder="这件纪念物对你们的意义..."></textarea>
                </div>

                <div class="form-actions">
                    <button type="button" class="cancel-btn" onclick="closeModal()">取消</button>
                    <button type="submit" class="save-btn">保存</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 引入前端配置 -->
    <script src="../scripts/config.js"></script>

    <!-- 智能视频加载器 - 四层CDN架构 -->
    <script src="../scripts/video-loader.js"></script>

    <script>
        // 纪念物数据管理
        let memorials = [
            {
                id: 1,
                icon: 'fas fa-gem',
                title: '第一条项链',
                date: '2023-05-14',
                description: '你送给我的第一份礼物，一条精美的银质项链，上面有一个小小的心形吊坠。虽然不是很昂贵，但那份心意比什么都珍贵。',
                significance: '这是我们爱情的第一个见证，也是我最珍爱的饰品。每当戴上它，就能感受到你的爱意。',
                special: 'special-memorial'
            },
            {
                id: 2,
                icon: 'fas fa-key',
                title: '爱情钥匙',
                date: '2023-06-20',
                description: '一把精致的小钥匙，你说这是你心房的钥匙，只有我才能打开。这把钥匙现在被我小心翼翼地保存着。',
                significance: '你给我的不只是一把钥匙，更是一份承诺，一个打开彼此心扉的约定。',
                special: 'treasure-box'
            },
            {
                id: 3,
                icon: 'fas fa-rose',
                title: '第一朵玫瑰',
                date: '2023-07-07',
                description: '七夕节那天，你送给我的第一朵红玫瑰。虽然花已经枯萎，但我把它制成了干花，永远保存着那份美好。',
                significance: '玫瑰会凋谢，但我们的爱情会永远绽放。这朵干花见证了我们的浪漫时光。'
            },
            {
                id: 4,
                icon: 'fas fa-camera',
                title: '拍立得相机',
                date: '2023-08-15',
                description: '你送给我的拍立得相机，说要记录我们在一起的每一个美好瞬间。现在我们已经拍了好多张珍贵的照片。',
                significance: '每一张照片都是我们爱情的片段，每一个瞬间都值得永远珍藏。'
            },
            {
                id: 5,
                icon: 'fas fa-book-heart',
                title: '爱情日记本',
                date: '2023-09-10',
                description: '一本精美的日记本，我们约定要在里面记录我们的爱情故事。每一页都写满了我们的甜蜜回忆和对未来的憧憬。',
                significance: '文字记录着我们的心情，日记见证着我们的成长，这是我们爱情最真实的记录。'
            }
        ];

        let nextId = 6;

        // 打开添加模态框
        function openAddModal() {
            document.getElementById('modalTitle').textContent = '添加纪念物';
            document.getElementById('memorialForm').reset();
            document.getElementById('memorialId').value = '';
            document.getElementById('memorialModal').style.display = 'flex';
        }

        // 编辑纪念物
        function editMemorial(id) {
            const memorial = memorials.find(m => m.id === id);
            if (!memorial) return;

            document.getElementById('modalTitle').textContent = '编辑纪念物';
            document.getElementById('memorialId').value = id;
            document.getElementById('memorialIcon').value = memorial.icon;
            document.getElementById('memorialTitle').value = memorial.title;
            document.getElementById('memorialDate').value = memorial.date;
            document.getElementById('memorialDescription').value = memorial.description;
            document.getElementById('memorialSignificance').value = memorial.significance;
            document.getElementById('memorialModal').style.display = 'flex';
        }

        // 删除纪念物
        function deleteMemorial(id) {
            if (confirm('确定要删除这个纪念物吗？')) {
                memorials = memorials.filter(m => m.id !== id);
                renderMemorials();
            }
        }

        // 关闭模态框
        function closeModal() {
            document.getElementById('memorialModal').style.display = 'none';
        }

        // 格式化日期
        function formatDate(dateString) {
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit'
            }).replace(/\//g, '年').replace(/年(\d+)年/, '年$1月') + '日';
        }

        // 渲染纪念物列表
        function renderMemorials() {
            const gallery = document.querySelector('.memorial-gallery');
            gallery.innerHTML = memorials.map(memorial => `
                <div class="memorial-item ${memorial.special || ''}" data-id="${memorial.id}">
                    <div class="memorial-actions">
                        <button class="action-btn edit-btn" onclick="editMemorial(${memorial.id})" title="编辑">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="action-btn delete-btn" onclick="deleteMemorial(${memorial.id})" title="删除">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                    <div class="memorial-icon">
                        <i class="${memorial.icon}"></i>
                    </div>
                    <h3 class="memorial-title">${memorial.title}</h3>
                    <div class="memorial-date">${formatDate(memorial.date)}</div>
                    <p class="memorial-description">${memorial.description}</p>
                    <div class="memorial-significance">"${memorial.significance}"</div>
                </div>
            `).join('');
        }

        // 表单提交处理
        document.getElementById('memorialForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const id = document.getElementById('memorialId').value;
            const memorialData = {
                icon: document.getElementById('memorialIcon').value,
                title: document.getElementById('memorialTitle').value,
                date: document.getElementById('memorialDate').value,
                description: document.getElementById('memorialDescription').value,
                significance: document.getElementById('memorialSignificance').value
            };

            if (id) {
                // 编辑现有纪念物
                const index = memorials.findIndex(m => m.id === parseInt(id));
                if (index !== -1) {
                    memorials[index] = { ...memorials[index], ...memorialData };
                }
            } else {
                // 添加新纪念物
                memorials.push({
                    id: nextId++,
                    ...memorialData
                });
            }

            renderMemorials();
            closeModal();
        });

        // 点击模态框外部关闭
        document.getElementById('memorialModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeModal();
            }
        });

        // 智能视频加载器集成 - 四层CDN架构
        const USE_SMART_LOADER = true; // 新旧系统切换开关

        const video = document.querySelector('.video-background video');
        const videoContainer = document.querySelector('.video-background');
        const loadingOverlay = document.getElementById('loadingOverlay');
        const loadingProgress = document.getElementById('loadingProgress');

        // 视频加载状态标记 (保持兼容性)
        let videoInitialized = false;
        let videoLoadingStarted = false;

        // 隐藏加载遮罩 (保持原有函数)
        function hideLoadingOverlay() {
            if (loadingProgress) loadingProgress.style.width = '100%';
            setTimeout(function() {
                if (loadingOverlay) loadingOverlay.classList.add('hidden');
            }, 300);
        }

        if (USE_SMART_LOADER && typeof VideoLoader !== 'undefined') {
            // 使用四层智能加载器
            console.log('🎬 启用四层智能视频加载器 - 纪念页面');

            VideoLoader.integrateWithPage({
                pageName: 'memorial',
                videoSelector: '.video-background video',
                loadingOverlaySelector: '#loadingOverlay',
                loadingProgressSelector: '#loadingProgress'
            });

        } else if (video) {
            // 降级到原有加载逻辑
            console.log('📼 使用原有视频加载逻辑 - 纪念页面');
            loadVideoWithOriginalMethod();
        } else {
            hideLoadingOverlay();
        }

        // 原有加载逻辑封装
        function loadVideoWithOriginalMethod() {

        // 模拟加载进度
        function updateProgress() {
            if (currentProgress < 90) {
                currentProgress += Math.random() * 15;
                if (currentProgress > 90) currentProgress = 90;
                loadingProgress.style.width = currentProgress + '%';
            }
        }

        // 隐藏加载遮罩
        function hideLoadingOverlay() {
            currentProgress = 100;
            loadingProgress.style.width = '100%';
            setTimeout(function() {
                loadingOverlay.classList.add('hidden');
            }, 300);
        }

        if (video) {
            // 开始进度模拟
            progressInterval = setInterval(updateProgress, 200);

            // 视频开始加载
            video.addEventListener('loadstart', function() {
                if (!videoLoadingStarted) {
                    console.log('海洋视频开始加载...');
                    videoLoadingStarted = true;
                }
            });

            // 视频加载进度
            video.addEventListener('progress', function() {
                if (!videoInitialized && video.buffered.length > 0) {
                    const buffered = video.buffered.end(0);
                    const duration = video.duration;
                    if (duration > 0) {
                        const realProgress = (buffered / duration) * 90;
                        if (realProgress > currentProgress) {
                            currentProgress = realProgress;
                            loadingProgress.style.width = currentProgress + '%';
                        }
                    }
                }
            });

            // 视频有足够数据可以播放
            video.addEventListener('canplay', function() {
                if (!videoInitialized) {
                    console.log('海洋视频可以播放');
                    clearInterval(progressInterval);
                    currentProgress = 95;
                    loadingProgress.style.width = currentProgress + '%';

                    // 确保视频自动播放
                    video.play().catch(function(error) {
                        console.log('视频自动播放被阻止:', error);
                        // 即使播放失败也隐藏加载遮罩
                        hideLoadingOverlay();
                    });
                }
            });

            // 视频加载完成并开始播放
            video.addEventListener('playing', function() {
                if (!videoInitialized) {
                    console.log('海洋视频背景加载成功并开始播放');
                    clearInterval(progressInterval);
                    video.classList.add('loaded');
                    videoContainer.classList.add('video-loaded');
                    hideLoadingOverlay();
                    videoInitialized = true; // 标记视频已初始化
                }
            });

            // 视频加载失败
            video.addEventListener('error', function() {
                if (!videoInitialized) {
                    console.log('海洋视频背景加载失败，使用海洋主题备用背景');
                    clearInterval(progressInterval);
                    video.style.display = 'none';
                    videoContainer.style.background = 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 25%, #0369a1 50%, #075985 75%, #0c4a6e 100%)';
                    videoContainer.classList.add('video-loaded');
                    hideLoadingOverlay();
                    videoInitialized = true;
                }
            });

            // 设置超时，如果视频加载时间过长，显示海洋主题背景
            setTimeout(function() {
                if (!video.classList.contains('loaded')) {
                    console.log('视频加载超时，切换到海洋主题背景');
                    clearInterval(progressInterval);
                    videoContainer.style.background = 'linear-gradient(135deg, #0ea5e9 0%, #0284c7 25%, #0369a1 50%, #075985 75%, #0c4a6e 100%)';
                    videoContainer.classList.add('video-loaded');
                    hideLoadingOverlay();
                }
            }, 10000); // 10秒超时
        } else {
            // 如果没有视频元素，直接隐藏加载遮罩
            hideLoadingOverlay();
        }

        // 页面卸载时清理视频资源
        window.addEventListener('beforeunload', function() {
            const video = document.querySelector('.video-background video');
            if (video) {
                video.pause();
                video.src = '';
                video.load();
                console.log('🧹 页面卸载，已清理视频资源');
            }
        });

        // 页面隐藏时暂停视频，显示时恢复播放
        document.addEventListener('visibilitychange', function() {
            const video = document.querySelector('.video-background video');
            if (video && videoInitialized) {
                if (document.hidden) {
                    video.pause();
                    console.log('📱 页面隐藏，暂停视频播放');
                } else {
                    video.play().catch(function(error) {
                        console.log('📱 页面显示，恢复视频播放失败:', error);
                    });
                    console.log('📱 页面显示，恢复视频播放');
                }
            }
        });
    </script>
</body>
</html>
