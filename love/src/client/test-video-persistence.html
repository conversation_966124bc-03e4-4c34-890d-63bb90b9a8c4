<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>视频资源持久化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
        }
        .test-container {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }
        .test-link {
            display: inline-block;
            color: #67e8f9;
            text-decoration: none;
            font-weight: bold;
            padding: 10px 20px;
            background: rgba(103, 232, 249, 0.1);
            border-radius: 8px;
            margin: 5px;
            border: 1px solid rgba(103, 232, 249, 0.3);
            transition: all 0.3s ease;
        }
        .test-link:hover {
            background: rgba(103, 232, 249, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(103, 232, 249, 0.3);
        }
        .status-indicator {
            display: inline-block;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            margin-left: 10px;
            font-weight: bold;
        }
        .status-success {
            background: #10b981;
            color: white;
        }
        .status-testing {
            background: #f59e0b;
            color: white;
        }
        .log-container {
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .log-entry {
            margin: 5px 0;
            padding: 3px 0;
        }
        .log-success { color: #10b981; }
        .log-warning { color: #f59e0b; }
        .log-error { color: #ef4444; }
        .log-info { color: #67e8f9; }
        h1 {
            text-align: center;
            margin-bottom: 30px;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }
        .instruction {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .navigation-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>🎬 视频资源持久化测试</h1>
    
    <div class="test-container">
        <h3>🔧 修复说明</h3>
        <p>已修改VideoLoader，使其在所有情况下都不卸载视频资源：</p>
        <ul>
            <li>✅ <strong>F5刷新</strong>：保持视频资源不卸载</li>
            <li>✅ <strong>页面导航</strong>：保持视频资源不卸载</li>
            <li>✅ <strong>链接跳转</strong>：保持视频资源不卸载</li>
            <li>✅ <strong>返回主页</strong>：保持视频资源不卸载</li>
        </ul>
    </div>

    <div class="test-container">
        <h3>🧪 测试方法</h3>
        <div class="instruction">
            <strong>测试步骤：</strong>
            <ol>
                <li>打开浏览器开发者工具 (F12)</li>
                <li>切换到 Console 标签页</li>
                <li>访问任意页面，观察视频加载日志</li>
                <li>点击下方导航链接，观察是否有"VideoLoader不清理视频资源"日志</li>
                <li>检查视频是否重新加载（应该不会重新加载）</li>
            </ol>
        </div>
    </div>

    <div class="test-container">
        <h3>🌐 页面导航测试</h3>
        <div class="navigation-grid">
            <a href="/" class="test-link">
                🏠 返回主页
                <span class="status-indicator status-testing">测试中</span>
            </a>
            <a href="/together-days" class="test-link">
                🌊 在一起的日子
                <span class="status-indicator status-testing">测试中</span>
            </a>
            <a href="/anniversary" class="test-link">
                🎉 纪念日
                <span class="status-indicator status-testing">测试中</span>
            </a>
            <a href="/meetings" class="test-link">
                💑 相遇记录
                <span class="status-indicator status-testing">测试中</span>
            </a>
            <a href="/memorial" class="test-link">
                🎁 纪念页面
                <span class="status-indicator status-testing">测试中</span>
            </a>
        </div>
    </div>

    <div class="test-container">
        <h3>📊 实时日志监控</h3>
        <div class="log-container" id="logContainer">
            <div class="log-entry log-info">等待日志输出...</div>
        </div>
        <button onclick="clearLogs()" style="background: rgba(239, 68, 68, 0.2); color: white; border: 1px solid rgba(239, 68, 68, 0.5); padding: 8px 16px; border-radius: 6px; cursor: pointer;">清空日志</button>
    </div>

    <div class="test-container">
        <h3>🎯 预期结果</h3>
        <ul>
            <li>✅ 页面导航时应该看到："VideoLoader保持视频资源不卸载，提供极速切换体验"</li>
            <li>✅ 不应该看到："页面真正卸载，VideoLoader清理视频资源"</li>
            <li>✅ 不应该看到："检测到页面导航，VideoLoader保持视频资源不卸载"</li>
            <li>✅ 视频不应该重新加载，切换页面应该很快</li>
            <li>✅ 背景视频应该保持连续播放状态</li>
        </ul>
    </div>

    <script>
        // 监听控制台日志
        const originalLog = console.log;
        const logContainer = document.getElementById('logContainer');
        
        function addLogEntry(message, type = 'info') {
            const entry = document.createElement('div');
            entry.className = `log-entry log-${type}`;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(entry);
            logContainer.scrollTop = logContainer.scrollHeight;
            
            // 保持最多50条日志
            const entries = logContainer.querySelectorAll('.log-entry');
            if (entries.length > 50) {
                entries[0].remove();
            }
        }
        
        // 重写console.log来捕获VideoLoader日志
        console.log = function(...args) {
            originalLog.apply(console, args);
            
            const message = args.join(' ');
            let type = 'info';
            
            if (message.includes('VideoLoader保持视频资源') || message.includes('不清理视频资源') || message.includes('极速切换体验')) {
                type = 'success';
            } else if (message.includes('清理视频资源') || message.includes('卸载')) {
                type = 'error';
            } else if (message.includes('VideoLoader') || message.includes('视频')) {
                type = 'info';
            } else if (message.includes('警告') || message.includes('超时')) {
                type = 'warning';
            }
            
            if (message.includes('VideoLoader') || message.includes('视频') || message.includes('页面卸载')) {
                addLogEntry(message, type);
            }
        };
        
        function clearLogs() {
            logContainer.innerHTML = '<div class="log-entry log-info">日志已清空，等待新的输出...</div>';
        }
        
        // 页面加载完成时的初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLogEntry('视频持久化测试页面已加载', 'success');
            addLogEntry('开始监控VideoLoader日志...', 'info');
            
            // 检查是否有VideoLoader实例
            if (window.VideoLoader) {
                addLogEntry('检测到VideoLoader类', 'success');
            } else {
                addLogEntry('未检测到VideoLoader类', 'warning');
            }
            
            if (window.videoLoader) {
                addLogEntry('检测到videoLoader实例', 'success');
            } else {
                addLogEntry('未检测到videoLoader实例', 'warning');
            }
        });
        
        // 监听页面卸载事件
        window.addEventListener('beforeunload', function() {
            addLogEntry('页面即将卸载，观察VideoLoader行为...', 'warning');
        });
        
        // 添加页面访问时间戳
        const timestamp = new Date().toLocaleString('zh-CN');
        const footer = document.createElement('div');
        footer.style.textAlign = 'center';
        footer.style.marginTop = '30px';
        footer.style.opacity = '0.7';
        footer.innerHTML = `<small>测试页面创建时间: ${timestamp}</small>`;
        document.body.appendChild(footer);
    </script>
</body>
</html>
